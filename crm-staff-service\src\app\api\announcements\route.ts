import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NotificationManager, createNotificationManager } from 'crm-shared-types';

// Initialize notification manager
const notificationManager = createNotificationManager();

// GET - Get active announcements
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const audience = url.searchParams.get('audience') as 'all' | 'staff' | 'students' | 'admins' | null;

    const announcements = notificationManager.getActiveAnnouncements(audience || 'staff');

    return NextResponse.json({
      success: true,
      data: announcements,
    });
  } catch (error) {
    console.error('Failed to get announcements:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'ANNOUNCEMENT_FETCH_FAILED',
        message: 'Failed to fetch announcements',
      },
    }, { status: 500 });
  }
}

// POST - Create announcement (admin/manager only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      }, { status: 401 });
    }

    // Check if user has permission to create announcements
    const userRole = session.user.role;
    if (!['manager', 'admin'].includes(userRole)) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Insufficient permissions to create announcements',
        },
      }, { status: 403 });
    }

    const body = await request.json();
    const { title, message, type, targetAudience, startDate, endDate } = body;

    if (!title || !message || !type || !targetAudience) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Title, message, type, and target audience are required',
        },
      }, { status: 400 });
    }

    const announcementId = notificationManager.createAnnouncement({
      title,
      message,
      type,
      targetAudience,
      isActive: true,
      startDate: startDate ? new Date(startDate) : new Date(),
      endDate: endDate ? new Date(endDate) : undefined,
      createdBy: session.user.email,
    });

    return NextResponse.json({
      success: true,
      data: {
        announcementId,
        message: 'Announcement created successfully',
      },
    });
  } catch (error) {
    console.error('Failed to create announcement:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'ANNOUNCEMENT_CREATE_FAILED',
        message: 'Failed to create announcement',
      },
    }, { status: 500 });
  }
}
