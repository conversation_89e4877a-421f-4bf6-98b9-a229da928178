import { NextRequest, NextResponse } from 'next/server';
import { withServiceAuth } from '@/lib/service-auth';
import { WebhookManager, createDataSyncManager } from 'crm-shared-types';
import { serviceClientFactory } from '@/lib/service-clients';

// Initialize webhook manager
const syncManager = createDataSyncManager(serviceClientFactory);
const webhookManager = new WebhookManager(serviceClientFactory, syncManager);

// Register webhook subscriptions for staff service
webhookManager.registerWebhook({
  url: `${process.env.STAFF_SERVICE_URL}/api/webhooks/receive`,
  events: [
    'payment.created',
    'payment.updated',
    'student.created',
    'student.updated',
  ],
  service: 'crm-staff-service',
  isActive: true,
  secret: process.env.WEBHOOK_SECRET,
});

export const POST = withServiceAuth(async (request: NextRequest) => {
  try {
    const body = await request.json();
    const { type, action, entityId, data } = body;

    // Emit webhook event for inter-service communication
    await webhookManager.emitEvent({
      type: `${type}.${action}`,
      source: 'crm-staff-service',
      data: {
        id: entityId,
        ...data,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Webhook event emitted successfully',
    });
  } catch (error) {
    console.error('Webhook emission failed:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'WEBHOOK_FAILED',
        message: 'Failed to emit webhook event',
      },
    }, { status: 500 });
  }
});

export const GET = withServiceAuth(async () => {
  try {
    const stats = webhookManager.getWebhookStats();
    const subscriptions = webhookManager.listSubscriptions();
    const recentDeliveries = webhookManager.listRecentDeliveries(10);

    return NextResponse.json({
      success: true,
      data: {
        stats,
        subscriptions,
        recentDeliveries,
      },
    });
  } catch (error) {
    console.error('Failed to get webhook status:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'WEBHOOK_STATUS_FAILED',
        message: 'Failed to get webhook status',
      },
    }, { status: 500 });
  }
});
