// Main export file for CRM Shared Types
export * from './types';

// Service authentication and communication utilities
export * from './lib/auth';
export * from './lib/service-clients';
export * from './lib/data-sync';
export * from './lib/webhooks';
export * from './lib/notifications';
export * from './lib/messaging';

// Version information
export const VERSION = '1.0.0';
export const PACKAGE_NAME = 'crm-shared-types';

// Service information
export const SERVICES = {
  STAFF: 'crm-staff-service',
  ADMIN: 'crm-admin-service', 
  STUDENT: 'crm-student-service',
  SHARED_TYPES: 'crm-shared-types'
} as const;

// Default configuration
export const DEFAULT_CONFIG = {
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 20,
    MAX_LIMIT: 100
  },
  SESSION: {
    DEFAULT_TIMEOUT: 3600, // 1 hour in seconds
    ADMIN_TIMEOUT: 1800,   // 30 minutes for admin
    STUDENT_TIMEOUT: 7200  // 2 hours for students
  },
  SECURITY: {
    MAX_LOGIN_ATTEMPTS: 5,
    LOCKOUT_DURATION: 900, // 15 minutes
    PASSWORD_MIN_LENGTH: 8,
    MFA_REQUIRED_FOR_ADMIN: true
  },
  FILE_UPLOAD: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png', 'gif'],
    UPLOAD_PATH: '/uploads'
  }
} as const;
