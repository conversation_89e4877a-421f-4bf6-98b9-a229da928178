const fs = require('fs');
const path = require('path');

// Create dist directory
if (!fs.existsSync('dist')) {
  fs.mkdirSync('dist', { recursive: true });
}

// Copy source files to dist
function copyDir(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }
  
  const files = fs.readdirSync(src);
  
  for (const file of files) {
    const srcPath = path.join(src, file);
    const destPath = path.join(dest, file);
    
    if (fs.statSync(srcPath).isDirectory()) {
      copyDir(srcPath, destPath);
    } else if (file.endsWith('.ts')) {
      // Convert .ts to .js and copy
      const content = fs.readFileSync(srcPath, 'utf8');
      const jsContent = content
        .replace(/export \* from ['"](.+)['"];/g, 'module.exports = { ...module.exports, ...require("$1") };')
        .replace(/export \{([^}]+)\} from ['"](.+)['"];/g, 'const { $1 } = require("$2"); module.exports = { ...module.exports, $1 };')
        .replace(/export (interface|type|enum|const|function|class)/g, 'exports.$1 = $1; //')
        .replace(/import .+ from ['"](.+)['"];/g, 'const $1 = require("$1");');
      
      fs.writeFileSync(destPath.replace('.ts', '.js'), jsContent);
    }
  }
}

// Enhanced approach: create a comprehensive index.js that exports everything
const indexContent = `
// CRM Shared Types - Main Export
module.exports = {
  // Version information
  VERSION: '1.0.0',
  PACKAGE_NAME: 'crm-shared-types',
  
  // Service authentication utilities
  ServiceClient: class ServiceClient {
    constructor(baseUrl, serviceName, apiKey) {
      this.baseUrl = baseUrl.replace(/\\/$/, '');
      this.serviceName = serviceName;
      this.apiKey = apiKey;
    }
    
    getAuthHeaders() {
      const timestamp = Date.now();
      const payload = \`\${this.serviceName}:\${timestamp}\`;
      const signature = Buffer.from(\`\${payload}:\${this.apiKey}\`).toString('base64');
      
      return {
        'X-Service-Name': this.serviceName,
        'X-Service-Timestamp': timestamp.toString(),
        'X-Service-Signature': signature,
        'Content-Type': 'application/json',
      };
    }
    
    async get(endpoint) {
      const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });
      
      if (!response.ok) {
        throw new Error(\`Service request failed: \${response.status} \${response.statusText}\`);
      }
      
      return response.json();
    }
    
    async post(endpoint, data) {
      const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(\`Service request failed: \${response.status} \${response.statusText}\`);
      }
      
      return response.json();
    }
    
    async put(endpoint, data) {
      const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(\`Service request failed: \${response.status} \${response.statusText}\`);
      }
      
      return response.json();
    }
    
    async delete(endpoint) {
      const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });
      
      if (!response.ok) {
        throw new Error(\`Service request failed: \${response.status} \${response.statusText}\`);
      }
      
      return response.json();
    }
  },
  
  // Service client factory
  ServiceClientFactory: class ServiceClientFactory {
    constructor(config) {
      this.config = config;
    }
    
    getAdminClient() {
      if (!this.adminClient) {
        this.adminClient = new module.exports.AdminServiceClient(
          this.config.adminServiceUrl,
          this.config.serviceName,
          this.config.apiKey
        );
      }
      return this.adminClient;
    }
    
    getStaffClient() {
      if (!this.staffClient) {
        this.staffClient = new module.exports.StaffServiceClient(
          this.config.staffServiceUrl,
          this.config.serviceName,
          this.config.apiKey
        );
      }
      return this.staffClient;
    }
    
    getStudentClient() {
      if (!this.studentClient) {
        this.studentClient = new module.exports.StudentServiceClient(
          this.config.studentServiceUrl,
          this.config.serviceName,
          this.config.apiKey
        );
      }
      return this.studentClient;
    }
  },
  
  // Service authentication middleware
  createServiceAuthMiddleware: function(config) {
    return function serviceAuthMiddleware(request) {
      const serviceName = request.headers.get('X-Service-Name');
      const timestamp = request.headers.get('X-Service-Timestamp');
      const signature = request.headers.get('X-Service-Signature');

      const authenticatedRequest = request;

      if (!serviceName || !timestamp || !signature) {
        authenticatedRequest.serviceAuth = {
          serviceName: 'unknown',
          isAuthenticated: false,
        };
        return authenticatedRequest;
      }

      if (!config.allowedServices.includes(serviceName)) {
        authenticatedRequest.serviceAuth = {
          serviceName,
          isAuthenticated: false,
        };
        return authenticatedRequest;
      }

      // Simple signature verification
      const expectedPayload = \`\${serviceName}:\${timestamp}\`;
      const expectedSignature = Buffer.from(\`\${expectedPayload}:\${config.apiKey}\`).toString('base64');

      const now = Date.now();
      const timestampAge = now - parseInt(timestamp);
      const maxAge = 5 * 60 * 1000; // 5 minutes

      const isValid = signature === expectedSignature && timestampAge <= maxAge;

      authenticatedRequest.serviceAuth = {
        serviceName,
        isAuthenticated: isValid,
      };

      return authenticatedRequest;
    };
  },

  // Notification Manager
  NotificationManager: class NotificationManager {
    constructor() {
      this.templates = new Map();
      this.notifications = new Map();
      this.announcements = new Map();
    }

    addTemplate(template) {
      this.templates.set(template.id, template);
    }

    getTemplate(templateId) {
      return this.templates.get(templateId);
    }

    async sendNotification(templateId, recipient, variables, options = {}) {
      const template = this.getTemplate(templateId);
      if (!template || !template.isActive) {
        throw new Error(\`Template \${templateId} not found or inactive\`);
      }

      const notification = {
        id: this.generateId(),
        type: template.type,
        recipient,
        subject: this.interpolateTemplate(template.subject, variables),
        message: this.interpolateTemplate(template.body, variables),
        data: variables,
        status: 'sent',
        priority: options.priority || 'normal',
        createdAt: new Date(),
      };

      this.notifications.set(notification.id, notification);
      return notification.id;
    }

    async sendDirectNotification(type, recipient, subject, message, options = {}) {
      const notification = {
        id: this.generateId(),
        type,
        recipient,
        subject,
        message,
        data: options.data,
        status: 'sent',
        priority: options.priority || 'normal',
        createdAt: new Date(),
      };

      this.notifications.set(notification.id, notification);
      return notification.id;
    }

    createAnnouncement(announcement) {
      const id = this.generateId();
      const newAnnouncement = {
        ...announcement,
        id,
        createdAt: new Date(),
      };

      this.announcements.set(id, newAnnouncement);
      return id;
    }

    getActiveAnnouncements(audience) {
      const now = new Date();
      return Array.from(this.announcements.values())
        .filter(announcement =>
          announcement.isActive &&
          announcement.startDate <= now &&
          (!announcement.endDate || announcement.endDate >= now) &&
          (!audience || announcement.targetAudience === 'all' || announcement.targetAudience === audience)
        );
    }

    getNotificationStats() {
      const notifications = Array.from(this.notifications.values());
      const stats = {
        total: notifications.length,
        pending: notifications.filter(n => n.status === 'pending').length,
        sent: notifications.filter(n => n.status === 'sent').length,
        failed: notifications.filter(n => n.status === 'failed').length,
        byType: {},
      };

      notifications.forEach(n => {
        stats.byType[n.type] = (stats.byType[n.type] || 0) + 1;
      });

      return stats;
    }

    interpolateTemplate(template, variables) {
      return template.replace(/\\{\\{(\\w+)\\}\\}/g, (match, key) => {
        return variables[key] || match;
      });
    }

    generateId() {
      return \`notif_\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}\`;
    }
  },

  // Messaging Manager
  MessagingManager: class MessagingManager {
    constructor() {
      this.threads = new Map();
      this.messages = new Map();
      this.userSubscriptions = new Map();
    }

    createThread(subject, participants, createdBy, options = {}) {
      const threadId = this.generateId();
      const thread = {
        id: threadId,
        subject,
        participants: participants.map(p => p.userId),
        participantDetails: participants,
        lastMessageAt: new Date(),
        unreadCount: {},
        isArchived: false,
        tags: options.tags || [],
        priority: options.priority || 'normal',
        createdBy,
        createdAt: new Date(),
      };

      participants.forEach(p => {
        thread.unreadCount[p.userId] = 0;
        this.subscribeUserToThread(p.userId, threadId);
      });

      this.threads.set(threadId, thread);
      return threadId;
    }

    sendMessage(threadId, senderId, senderName, senderRole, content, options = {}) {
      const thread = this.threads.get(threadId);
      if (!thread) {
        throw new Error(\`Thread \${threadId} not found\`);
      }

      const messageId = this.generateId();
      const message = {
        id: messageId,
        threadId,
        senderId,
        senderName,
        senderRole,
        content,
        attachments: options.attachments || [],
        readBy: [{ userId: senderId, readAt: new Date() }],
        sentAt: new Date(),
      };

      this.messages.set(messageId, message);

      thread.lastMessageAt = new Date();
      thread.lastMessage = content.substring(0, 100) + (content.length > 100 ? '...' : '');

      thread.participants.forEach(participantId => {
        if (participantId !== senderId) {
          thread.unreadCount[participantId] = (thread.unreadCount[participantId] || 0) + 1;
        }
      });

      return messageId;
    }

    getUserThreads(userId, filter = {}) {
      const userThreadIds = this.userSubscriptions.get(userId) || new Set();
      let threads = Array.from(userThreadIds)
        .map(threadId => this.threads.get(threadId))
        .filter(thread => thread !== undefined);

      if (filter.unreadOnly) {
        threads = threads.filter(thread => (thread.unreadCount[userId] || 0) > 0);
      }

      return threads.sort((a, b) => b.lastMessageAt.getTime() - a.lastMessageAt.getTime());
    }

    getMessagingStats(userId) {
      const userThreadIds = this.userSubscriptions.get(userId) || new Set();
      const userThreads = Array.from(userThreadIds)
        .map(threadId => this.threads.get(threadId))
        .filter(thread => thread !== undefined);

      const totalUnreadMessages = userThreads.reduce((sum, thread) => sum + (thread.unreadCount[userId] || 0), 0);

      return {
        totalThreads: userThreads.length,
        unreadThreads: userThreads.filter(thread => (thread.unreadCount[userId] || 0) > 0).length,
        totalMessages: Array.from(this.messages.values()).filter(message =>
          userThreads.some(thread => thread.id === message.threadId)
        ).length,
        unreadMessages: totalUnreadMessages,
      };
    }

    subscribeUserToThread(userId, threadId) {
      if (!this.userSubscriptions.has(userId)) {
        this.userSubscriptions.set(userId, new Set());
      }
      this.userSubscriptions.get(userId).add(threadId);
    }

    generateId() {
      return \`msg_\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}\`;
    }
  },

  // Data Sync Manager
  DataSyncManager: class DataSyncManager {
    constructor(serviceFactory) {
      this.serviceFactory = serviceFactory;
      this.syncQueue = [];
      this.isProcessing = false;
    }

    async queueSync(event) {
      const syncEvent = {
        ...event,
        id: this.generateId(),
        timestamp: new Date(),
      };

      this.syncQueue.push(syncEvent);

      if (!this.isProcessing) {
        await this.processQueue();
      }
    }

    async processQueue() {
      if (this.isProcessing || this.syncQueue.length === 0) {
        return;
      }

      this.isProcessing = true;

      try {
        while (this.syncQueue.length > 0) {
          const event = this.syncQueue.shift();
          if (event) {
            await this.processSyncEvent(event);
          }
        }
      } finally {
        this.isProcessing = false;
      }
    }

    async processSyncEvent(event) {
      // Simple sync processing - in real implementation would call actual services
      console.log(\`Processing sync event: \${event.entity} \${event.type}\`);
    }

    getQueueStatus() {
      return {
        queueLength: this.syncQueue.length,
        isProcessing: this.isProcessing,
      };
    }

    generateId() {
      return \`sync_\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}\`;
    }
  },

  // Factory functions
  createNotificationManager: function() {
    return new module.exports.NotificationManager();
  },

  createMessagingManager: function() {
    return new module.exports.MessagingManager();
  },

  createDataSyncManager: function(serviceFactory) {
    return new module.exports.DataSyncManager(serviceFactory);
  }
};

// Extend ServiceClient for specific services
class AdminServiceClient extends module.exports.ServiceClient {
  async getPayments(page = 1, limit = 20) {
    return this.get(\`/api/payments?page=\${page}&limit=\${limit}\`);
  }
  
  async createPayment(data) {
    return this.post('/api/payments', data);
  }
  
  async createUser(userData) {
    return this.post('/api/users', userData);
  }
}

class StaffServiceClient extends module.exports.ServiceClient {
  async getLeads(status, page = 1, limit = 20) {
    const query = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
    if (status) query.append('status', status);
    return this.get(\`/api/leads?\${query}\`);
  }
  
  async getCourses(page = 1, limit = 20) {
    return this.get(\`/api/courses?page=\${page}&limit=\${limit}\`);
  }
  
  async getCourse(courseId) {
    return this.get(\`/api/courses/\${courseId}\`);
  }
  
  async getGroups(courseId) {
    const query = courseId ? \`?courseId=\${courseId}\` : '';
    return this.get(\`/api/groups\${query}\`);
  }
  
  async getAssignments(studentId) {
    const query = studentId ? \`?studentId=\${studentId}\` : '';
    return this.get(\`/api/assignments\${query}\`);
  }
  
  async getResources(courseId, groupId) {
    const query = new URLSearchParams();
    if (courseId) query.append('courseId', courseId);
    if (groupId) query.append('groupId', groupId);
    return this.get(\`/api/resources?\${query}\`);
  }
}

class StudentServiceClient extends module.exports.ServiceClient {
  async getStudent(studentId) {
    return this.get(\`/api/students/\${studentId}\`);
  }
  
  async createStudent(studentData) {
    return this.post('/api/students', studentData);
  }
  
  async getProgress(studentId) {
    return this.get(\`/api/progress?studentId=\${studentId}\`);
  }
  
  async updateProgress(progressData) {
    return this.post('/api/progress', progressData);
  }
  
  async syncSchedule(studentId) {
    return this.post('/api/schedule/sync', { studentId });
  }
}

module.exports.AdminServiceClient = AdminServiceClient;
module.exports.StaffServiceClient = StaffServiceClient;
module.exports.StudentServiceClient = StudentServiceClient;
`;

fs.writeFileSync('dist/index.js', indexContent);

// Create a simple index.d.ts
const dtsContent = `
export declare const VERSION: string;
export declare const PACKAGE_NAME: string;

export interface ServiceAuthConfig {
  serviceName: string;
  apiKey: string;
  allowedServices: string[];
}

export declare class ServiceClient {
  constructor(baseUrl: string, serviceName: string, apiKey: string);
  get<T>(endpoint: string): Promise<T>;
  post<T>(endpoint: string, data: any): Promise<T>;
  put<T>(endpoint: string, data: any): Promise<T>;
  delete<T>(endpoint: string): Promise<T>;
}

export declare class AdminServiceClient extends ServiceClient {
  getPayments(page?: number, limit?: number): Promise<any>;
  createPayment(data: any): Promise<any>;
  createUser(userData: any): Promise<any>;
}

export declare class StaffServiceClient extends ServiceClient {
  getLeads(status?: string, page?: number, limit?: number): Promise<any>;
  getCourses(page?: number, limit?: number): Promise<any>;
  getCourse(courseId: string): Promise<any>;
  getGroups(courseId?: string): Promise<any>;
  getAssignments(studentId?: string): Promise<any>;
  getResources(courseId?: string, groupId?: string): Promise<any>;
}

export declare class StudentServiceClient extends ServiceClient {
  getStudent(studentId: string): Promise<any>;
  createStudent(studentData: any): Promise<any>;
  getProgress(studentId: string): Promise<any>;
  updateProgress(progressData: any): Promise<any>;
  syncSchedule(studentId: string): Promise<any>;
}

export declare class ServiceClientFactory {
  constructor(config: {
    adminServiceUrl: string;
    staffServiceUrl: string;
    studentServiceUrl: string;
    serviceName: string;
    apiKey: string;
  });
  getAdminClient(): AdminServiceClient;
  getStaffClient(): StaffServiceClient;
  getStudentClient(): StudentServiceClient;
}

export declare function createServiceAuthMiddleware(config: ServiceAuthConfig): (request: any) => any;
`;

fs.writeFileSync('dist/index.d.ts', dtsContent);

console.log('Build completed successfully!');
