// Performance Monitoring and Optimization Utilities

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  timestamp: Date;
  context?: Record<string, any>;
}

export interface PerformanceReport {
  id: string;
  service: string;
  metrics: PerformanceMetric[];
  summary: {
    averageResponseTime: number;
    totalRequests: number;
    errorRate: number;
    memoryUsage: number;
  };
  generatedAt: Date;
}

// Performance Monitor
export class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private requestTimes: Map<string, number> = new Map();

  // Start timing a request
  startTiming(requestId: string): void {
    this.requestTimes.set(requestId, Date.now());
  }

  // End timing and record metric
  endTiming(requestId: string, operation: string, context?: Record<string, any>): void {
    const startTime = this.requestTimes.get(requestId);
    if (startTime) {
      const duration = Date.now() - startTime;
      this.recordMetric({
        name: operation,
        value: duration,
        unit: 'ms',
        timestamp: new Date(),
        context,
      });
      this.requestTimes.delete(requestId);
    }
  }

  // Record a performance metric
  recordMetric(metric: PerformanceMetric): void {
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }
    
    const metricList = this.metrics.get(metric.name)!;
    metricList.push(metric);
    
    // Keep only last 1000 metrics per type
    if (metricList.length > 1000) {
      metricList.shift();
    }
  }

  // Get metrics for a specific operation
  getMetrics(operation: string): PerformanceMetric[] {
    return this.metrics.get(operation) || [];
  }

  // Get average response time for an operation
  getAverageResponseTime(operation: string): number {
    const metrics = this.getMetrics(operation);
    if (metrics.length === 0) return 0;
    
    const total = metrics.reduce((sum, metric) => sum + metric.value, 0);
    return total / metrics.length;
  }

  // Get performance report
  generateReport(service: string): PerformanceReport {
    const allMetrics: PerformanceMetric[] = [];
    let totalRequests = 0;
    let totalResponseTime = 0;
    let errorCount = 0;

    for (const [, metrics] of this.metrics.entries()) {
      allMetrics.push(...metrics);
      totalRequests += metrics.length;
      totalResponseTime += metrics.reduce((sum, m) => sum + m.value, 0);

      // Count errors (assuming response times > 5000ms are errors)
      errorCount += metrics.filter(m => m.value > 5000).length;
    }

    return {
      id: this.generateId(),
      service,
      metrics: allMetrics,
      summary: {
        averageResponseTime: totalRequests > 0 ? totalResponseTime / totalRequests : 0,
        totalRequests,
        errorRate: totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0,
        memoryUsage: this.getMemoryUsage(),
      },
      generatedAt: new Date(),
    };
  }

  // Get memory usage (Node.js specific)
  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const usage = process.memoryUsage();
      return usage.heapUsed / 1024 / 1024; // Convert to MB
    }
    return 0;
  }

  // Clear old metrics
  clearOldMetrics(olderThanHours: number = 24): void {
    const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
    
    for (const [operation, metrics] of this.metrics.entries()) {
      const filteredMetrics = metrics.filter(m => m.timestamp > cutoffTime);
      this.metrics.set(operation, filteredMetrics);
    }
  }

  private generateId(): string {
    return `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Performance middleware for API routes
export function createPerformanceMiddleware(monitor: PerformanceMonitor) {
  return function performanceMiddleware(
    handler: (request: any) => Promise<any>
  ) {
    return async function wrappedHandler(request: any): Promise<any> {
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const operation = `${request.method} ${request.url}`;
      
      monitor.startTiming(requestId);
      
      try {
        const result = await handler(request);
        monitor.endTiming(requestId, operation, {
          status: 'success',
          method: request.method,
          url: request.url,
        });
        return result;
      } catch (error) {
        monitor.endTiming(requestId, operation, {
          status: 'error',
          method: request.method,
          url: request.url,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
      }
    };
  };
}

// Database query performance tracker
export class DatabasePerformanceTracker {
  private monitor: PerformanceMonitor;

  constructor(monitor: PerformanceMonitor) {
    this.monitor = monitor;
  }

  // Wrap database queries with performance tracking
  trackQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    const requestId = `db_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.monitor.startTiming(requestId);
    
    return queryFn()
      .then(result => {
        this.monitor.endTiming(requestId, `db_${queryName}`, {
          ...context,
          status: 'success',
        });
        return result;
      })
      .catch(error => {
        this.monitor.endTiming(requestId, `db_${queryName}`, {
          ...context,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
      });
  }
}

// Cache performance utilities
export class CacheManager {
  private cache: Map<string, { data: any; expiry: number }> = new Map();
  private monitor: PerformanceMonitor;

  constructor(monitor: PerformanceMonitor) {
    this.monitor = monitor;
  }

  // Get from cache
  get<T>(key: string): T | null {
    const startTime = Date.now();
    const item = this.cache.get(key);
    
    if (!item || Date.now() > item.expiry) {
      this.monitor.recordMetric({
        name: 'cache_miss',
        value: Date.now() - startTime,
        unit: 'ms',
        timestamp: new Date(),
        context: { key },
      });
      return null;
    }
    
    this.monitor.recordMetric({
      name: 'cache_hit',
      value: Date.now() - startTime,
      unit: 'ms',
      timestamp: new Date(),
      context: { key },
    });
    
    return item.data;
  }

  // Set cache item
  set(key: string, data: any, ttlSeconds: number = 300): void {
    const expiry = Date.now() + ttlSeconds * 1000;
    this.cache.set(key, { data, expiry });
    
    this.monitor.recordMetric({
      name: 'cache_set',
      value: 1,
      unit: 'count',
      timestamp: new Date(),
      context: { key, ttl: ttlSeconds },
    });
  }

  // Clear expired items
  clearExpired(): void {
    const now = Date.now();
    let clearedCount = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
        clearedCount++;
      }
    }
    
    this.monitor.recordMetric({
      name: 'cache_cleanup',
      value: clearedCount,
      unit: 'count',
      timestamp: new Date(),
    });
  }

  // Get cache statistics
  getStats(): {
    size: number;
    hitRate: number;
    missRate: number;
  } {
    const hits = this.monitor.getMetrics('cache_hit').length;
    const misses = this.monitor.getMetrics('cache_miss').length;
    const total = hits + misses;
    
    return {
      size: this.cache.size,
      hitRate: total > 0 ? (hits / total) * 100 : 0,
      missRate: total > 0 ? (misses / total) * 100 : 0,
    };
  }
}

// Rate limiting utility
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private monitor: PerformanceMonitor;

  constructor(monitor: PerformanceMonitor) {
    this.monitor = monitor;
  }

  // Check if request is allowed
  isAllowed(
    identifier: string,
    maxRequests: number,
    windowSeconds: number
  ): boolean {
    const now = Date.now();
    const windowStart = now - windowSeconds * 1000;
    
    if (!this.requests.has(identifier)) {
      this.requests.set(identifier, []);
    }
    
    const userRequests = this.requests.get(identifier)!;
    
    // Remove old requests
    const validRequests = userRequests.filter(time => time > windowStart);
    this.requests.set(identifier, validRequests);
    
    const isAllowed = validRequests.length < maxRequests;
    
    if (isAllowed) {
      validRequests.push(now);
    }
    
    this.monitor.recordMetric({
      name: 'rate_limit_check',
      value: isAllowed ? 1 : 0,
      unit: 'count',
      timestamp: new Date(),
      context: {
        identifier,
        allowed: isAllowed,
        currentRequests: validRequests.length,
        maxRequests,
      },
    });
    
    return isAllowed;
  }
}

// Export factory functions
export function createPerformanceMonitor(): PerformanceMonitor {
  return new PerformanceMonitor();
}

export function createDatabaseTracker(monitor: PerformanceMonitor): DatabasePerformanceTracker {
  return new DatabasePerformanceTracker(monitor);
}

export function createCacheManager(monitor: PerformanceMonitor): CacheManager {
  return new CacheManager(monitor);
}

export function createRateLimiter(monitor: PerformanceMonitor): RateLimiter {
  return new RateLimiter(monitor);
}
