(()=>{var e={};e.id=580,e.ids=[580],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},6599:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>S,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>f});var r={};s.r(r),s.d(r,{GET:()=>g,POST:()=>p});var i=s(6559),n=s(8088),a=s(7719),o=s(2190),c=s(8731),u=s(7582);let d=new u.ServiceClientFactory({adminServiceUrl:process.env.ADMIN_SERVICE_URL||"http://localhost:3001",staffServiceUrl:process.env.STAFF_SERVICE_URL||"http://localhost:3002",studentServiceUrl:process.env.STUDENT_SERVICE_URL||"http://localhost:3003",serviceName:"crm-admin-service",apiKey:process.env.SERVICE_API_KEY||"default-api-key"}),h=(0,u.createDataSyncManager)(d),l=new u.WebhookManager(d,h);l.registerWebhook({url:`${process.env.ADMIN_SERVICE_URL}/api/webhooks/receive`,events:["lead.created","lead.updated","student.created","student.updated","course.created","course.updated"],service:"crm-admin-service",isActive:!0,secret:process.env.WEBHOOK_SECRET});let p=(0,c.A0)(async e=>{try{let{type:t,action:s,entityId:r,data:i}=await e.json();return await l.emitEvent({type:`${t}.${s}`,source:"crm-admin-service",data:{id:r,...i}}),o.NextResponse.json({success:!0,message:"Webhook event emitted successfully"})}catch(e){return console.error("Webhook emission failed:",e),o.NextResponse.json({success:!1,error:{code:"WEBHOOK_FAILED",message:"Failed to emit webhook event"}},{status:500})}}),g=(0,c.A0)(async()=>{try{let e=l.getWebhookStats(),t=l.listSubscriptions(),s=l.listRecentDeliveries(10);return o.NextResponse.json({success:!0,data:{stats:e,subscriptions:t,recentDeliveries:s}})}catch(e){return console.error("Failed to get webhook status:",e),o.NextResponse.json({success:!1,error:{code:"WEBHOOK_STATUS_FAILED",message:"Failed to get webhook status"}},{status:500})}}),m=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/webhooks/route",pathname:"/api/webhooks",filename:"route",bundlePath:"app/api/webhooks/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\webhooks\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:v,workUnitAsyncStorage:f,serverHooks:y}=m;function S(){return(0,a.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:f})}},7582:e=>{"use strict";e.exports={VERSION:"1.0.0",PACKAGE_NAME:"crm-shared-types",ServiceClient:class{constructor(e,t,s){this.baseUrl=e.replace(/\/$/,""),this.serviceName=t,this.apiKey=s}getAuthHeaders(){let e=Date.now(),t=`${this.serviceName}:${e}`,s=Buffer.from(`${t}:${this.apiKey}`).toString("base64");return{"X-Service-Name":this.serviceName,"X-Service-Timestamp":e.toString(),"X-Service-Signature":s,"Content-Type":"application/json"}}async get(e){let t=await fetch(`${this.baseUrl}${e}`,{method:"GET",headers:this.getAuthHeaders()});if(!t.ok)throw Error(`Service request failed: ${t.status} ${t.statusText}`);return t.json()}async post(e,t){let s=await fetch(`${this.baseUrl}${e}`,{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!s.ok)throw Error(`Service request failed: ${s.status} ${s.statusText}`);return s.json()}async put(e,t){let s=await fetch(`${this.baseUrl}${e}`,{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!s.ok)throw Error(`Service request failed: ${s.status} ${s.statusText}`);return s.json()}async delete(e){let t=await fetch(`${this.baseUrl}${e}`,{method:"DELETE",headers:this.getAuthHeaders()});if(!t.ok)throw Error(`Service request failed: ${t.status} ${t.statusText}`);return t.json()}},ServiceClientFactory:class{constructor(e){this.config=e}getAdminClient(){return this.adminClient||(this.adminClient=new e.exports.AdminServiceClient(this.config.adminServiceUrl,this.config.serviceName,this.config.apiKey)),this.adminClient}getStaffClient(){return this.staffClient||(this.staffClient=new e.exports.StaffServiceClient(this.config.staffServiceUrl,this.config.serviceName,this.config.apiKey)),this.staffClient}getStudentClient(){return this.studentClient||(this.studentClient=new e.exports.StudentServiceClient(this.config.studentServiceUrl,this.config.serviceName,this.config.apiKey)),this.studentClient}},createServiceAuthMiddleware:function(e){return function(t){let s=t.headers.get("X-Service-Name"),r=t.headers.get("X-Service-Timestamp"),i=t.headers.get("X-Service-Signature");if(!s||!r||!i)return t.serviceAuth={serviceName:"unknown",isAuthenticated:!1},t;if(!e.allowedServices.includes(s))return t.serviceAuth={serviceName:s,isAuthenticated:!1},t;let n=`${s}:${r}`,a=Buffer.from(`${n}:${e.apiKey}`).toString("base64"),o=Date.now()-parseInt(r);return t.serviceAuth={serviceName:s,isAuthenticated:i===a&&o<=3e5},t}},NotificationManager:class{constructor(){this.templates=new Map,this.notifications=new Map,this.announcements=new Map}addTemplate(e){this.templates.set(e.id,e)}getTemplate(e){return this.templates.get(e)}async sendNotification(e,t,s,r={}){let i=this.getTemplate(e);if(!i||!i.isActive)throw Error(`Template ${e} not found or inactive`);let n={id:this.generateId(),type:i.type,recipient:t,subject:this.interpolateTemplate(i.subject,s),message:this.interpolateTemplate(i.body,s),data:s,status:"sent",priority:r.priority||"normal",createdAt:new Date};return this.notifications.set(n.id,n),n.id}async sendDirectNotification(e,t,s,r,i={}){let n={id:this.generateId(),type:e,recipient:t,subject:s,message:r,data:i.data,status:"sent",priority:i.priority||"normal",createdAt:new Date};return this.notifications.set(n.id,n),n.id}createAnnouncement(e){let t=this.generateId(),s={...e,id:t,createdAt:new Date};return this.announcements.set(t,s),t}getActiveAnnouncements(e){let t=new Date;return Array.from(this.announcements.values()).filter(s=>s.isActive&&s.startDate<=t&&(!s.endDate||s.endDate>=t)&&(!e||"all"===s.targetAudience||s.targetAudience===e))}getNotificationStats(){let e=Array.from(this.notifications.values()),t={total:e.length,pending:e.filter(e=>"pending"===e.status).length,sent:e.filter(e=>"sent"===e.status).length,failed:e.filter(e=>"failed"===e.status).length,byType:{}};return e.forEach(e=>{t.byType[e.type]=(t.byType[e.type]||0)+1}),t}interpolateTemplate(e,t){return e.replace(/\{\{(\w+)\}\}/g,(e,s)=>t[s]||e)}generateId(){return`notif_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}},MessagingManager:class{constructor(){this.threads=new Map,this.messages=new Map,this.userSubscriptions=new Map}createThread(e,t,s,r={}){let i=this.generateId(),n={id:i,subject:e,participants:t.map(e=>e.userId),participantDetails:t,lastMessageAt:new Date,unreadCount:{},isArchived:!1,tags:r.tags||[],priority:r.priority||"normal",createdBy:s,createdAt:new Date};return t.forEach(e=>{n.unreadCount[e.userId]=0,this.subscribeUserToThread(e.userId,i)}),this.threads.set(i,n),i}sendMessage(e,t,s,r,i,n={}){let a=this.threads.get(e);if(!a)throw Error(`Thread ${e} not found`);let o=this.generateId(),c={id:o,threadId:e,senderId:t,senderName:s,senderRole:r,content:i,attachments:n.attachments||[],readBy:[{userId:t,readAt:new Date}],sentAt:new Date};return this.messages.set(o,c),a.lastMessageAt=new Date,a.lastMessage=i.substring(0,100)+(i.length>100?"...":""),a.participants.forEach(e=>{e!==t&&(a.unreadCount[e]=(a.unreadCount[e]||0)+1)}),o}getUserThreads(e,t={}){let s=Array.from(this.userSubscriptions.get(e)||new Set).map(e=>this.threads.get(e)).filter(e=>void 0!==e);return t.unreadOnly&&(s=s.filter(t=>(t.unreadCount[e]||0)>0)),s.sort((e,t)=>t.lastMessageAt.getTime()-e.lastMessageAt.getTime())}getMessagingStats(e){let t=Array.from(this.userSubscriptions.get(e)||new Set).map(e=>this.threads.get(e)).filter(e=>void 0!==e),s=t.reduce((t,s)=>t+(s.unreadCount[e]||0),0);return{totalThreads:t.length,unreadThreads:t.filter(t=>(t.unreadCount[e]||0)>0).length,totalMessages:Array.from(this.messages.values()).filter(e=>t.some(t=>t.id===e.threadId)).length,unreadMessages:s}}subscribeUserToThread(e,t){this.userSubscriptions.has(e)||this.userSubscriptions.set(e,new Set),this.userSubscriptions.get(e).add(t)}generateId(){return`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}},DataSyncManager:class{constructor(e){this.serviceFactory=e,this.syncQueue=[],this.isProcessing=!1}async queueSync(e){let t={...e,id:this.generateId(),timestamp:new Date};this.syncQueue.push(t),this.isProcessing||await this.processQueue()}async processQueue(){if(!this.isProcessing&&0!==this.syncQueue.length){this.isProcessing=!0;try{for(;this.syncQueue.length>0;){let e=this.syncQueue.shift();e&&await this.processSyncEvent(e)}}finally{this.isProcessing=!1}}}async processSyncEvent(e){console.log(`Processing sync event: ${e.entity} ${e.type}`)}getQueueStatus(){return{queueLength:this.syncQueue.length,isProcessing:this.isProcessing}}generateId(){return`sync_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}},createNotificationManager:function(){return new e.exports.NotificationManager},createMessagingManager:function(){return new e.exports.MessagingManager},createDataSyncManager:function(t){return new e.exports.DataSyncManager(t)}};class t extends e.exports.ServiceClient{async getPayments(e=1,t=20){return this.get(`/api/payments?page=${e}&limit=${t}`)}async createPayment(e){return this.post("/api/payments",e)}async createUser(e){return this.post("/api/users",e)}}class s extends e.exports.ServiceClient{async getLeads(e,t=1,s=20){let r=new URLSearchParams({page:t.toString(),limit:s.toString()});return e&&r.append("status",e),this.get(`/api/leads?${r}`)}async getCourses(e=1,t=20){return this.get(`/api/courses?page=${e}&limit=${t}`)}async getCourse(e){return this.get(`/api/courses/${e}`)}async getGroups(e){let t=e?`?courseId=${e}`:"";return this.get(`/api/groups${t}`)}async getAssignments(e){let t=e?`?studentId=${e}`:"";return this.get(`/api/assignments${t}`)}async getResources(e,t){let s=new URLSearchParams;return e&&s.append("courseId",e),t&&s.append("groupId",t),this.get(`/api/resources?${s}`)}}class r extends e.exports.ServiceClient{async getStudent(e){return this.get(`/api/students/${e}`)}async createStudent(e){return this.post("/api/students",e)}async getProgress(e){return this.get(`/api/progress?studentId=${e}`)}async updateProgress(e){return this.post("/api/progress",e)}async syncSchedule(e){return this.post("/api/schedule/sync",{studentId:e})}}e.exports.AdminServiceClient=t,e.exports.StaffServiceClient=s,e.exports.StudentServiceClient=r},8335:()=>{},8731:(e,t,s)=>{"use strict";s.d(t,{A0:()=>o,TB:()=>c,_q:()=>u});var r=s(2190),i=s(7582);let n={serviceName:"crm-admin-service",apiKey:process.env.SERVICE_API_KEY||"default-api-key",allowedServices:["crm-staff-service","crm-student-service","crm-admin-service"]},a=(0,i.createServiceAuthMiddleware)(n);function o(e){return async function(t){let s=a(t);return t.url.includes("/api/service/")&&!s.serviceAuth?.isAuthenticated?r.NextResponse.json({success:!1,error:{code:"UNAUTHORIZED",message:"Service authentication required"}},{status:401}):e(s)}}function c(e){return e.serviceAuth?.isAuthenticated?e.serviceAuth.serviceName:null}async function u(){try{return r.NextResponse.json({service:"crm-admin-service",status:"healthy",timestamp:new Date().toISOString(),version:"1.0.0",allowedServices:n.allowedServices})}catch(e){return r.NextResponse.json({service:"crm-admin-service",status:"unhealthy",timestamp:new Date().toISOString(),error:"Service health check failed"},{status:500})}}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,199],()=>s(6599));module.exports=r})();