(()=>{var e={};e.id=647,e.ids=[647],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1390:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>f,serverHooks:()=>N,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>v});var a={};t.r(a),t.d(a,{GET:()=>m,POST:()=>y});var s=t(6559),n=t(8088),i=t(7719),o=t(2190),d=t(2909),u=t(5069),c=t(6330),p=t(5697);let l=p.z.object({reportType:p.z.enum(["revenue","payments","transactions"]),periodStart:p.z.string().transform(e=>new Date(e)),periodEnd:p.z.string().transform(e=>new Date(e))});async function m(e){try{let r=await (0,d.j2)();if(!r?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),a=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"10"),n=t.get("reportType"),i=(a-1)*s,c={};n&&(c.reportType=n);let[p,l]=await Promise.all([u.z.financialReport.findMany({where:c,skip:i,take:s,include:{generatedByUser:{select:{firstName:!0,lastName:!0,email:!0}}},orderBy:{createdAt:"desc"}}),u.z.financialReport.count({where:c})]);return o.NextResponse.json({success:!0,data:{reports:p,pagination:{page:a,limit:s,total:l,pages:Math.ceil(l/s)}}})}catch(e){return console.error("Error fetching reports:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e){try{let r=await (0,d.j2)();if(!r?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});if(!["admin","accounting"].includes(r.user.role))return o.NextResponse.json({error:"Forbidden"},{status:403});let t=await e.json(),{reportType:a,periodStart:s,periodEnd:n}=l.parse(t),i={};switch(a){case"revenue":i=await w(s,n);break;case"payments":i=await g(s,n);break;case"transactions":i=await h(s,n)}let c=await u.z.financialReport.create({data:{reportType:a,periodStart:s,periodEnd:n,data:i,generatedBy:r.user.id},include:{generatedByUser:{select:{firstName:!0,lastName:!0,email:!0}}}});return await u.z.auditLog.create({data:{userId:r.user.id,action:"GENERATE_REPORT",resourceType:"FINANCIAL_REPORT",resourceId:c.id,newValues:{reportType:a,periodStart:s,periodEnd:n},ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:c})}catch(e){if(e instanceof p.z.ZodError)return o.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error generating report:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function w(e,r){let t=await u.z.paymentRecord.findMany({where:{paymentDate:{gte:e,lte:r},status:c.PaymentStatus.verified}}),a=t.reduce((e,r)=>e+Number(r.amount),0),s=t.length;return{summary:{totalRevenue:a,paymentCount:s,averagePayment:s>0?a/s:0},revenueByMethod:t.reduce((e,r)=>(e[r.paymentMethod]=(e[r.paymentMethod]||0)+Number(r.amount),e),{}),periodStart:e,periodEnd:r}}async function g(e,r){let t=await u.z.paymentRecord.findMany({where:{paymentDate:{gte:e,lte:r}},include:{recordedByUser:{select:{firstName:!0,lastName:!0}}}}),a=t.reduce((e,r)=>(e[r.status]=(e[r.status]||0)+1,e),{}),s=t.reduce((e,r)=>(e[r.paymentMethod]=(e[r.paymentMethod]||0)+1,e),{});return{summary:{totalPayments:t.length,paymentsByStatus:a,paymentsByMethod:s},payments:t.map(e=>({id:e.id,amount:e.amount,paymentDate:e.paymentDate,paymentMethod:e.paymentMethod,status:e.status,recordedBy:`${e.recordedByUser.firstName} ${e.recordedByUser.lastName}`})),periodStart:e,periodEnd:r}}async function h(e,r){let t=await u.z.financialTransaction.findMany({where:{timestamp:{gte:e,lte:r}},include:{performedByUser:{select:{firstName:!0,lastName:!0}}}}),a=t.reduce((e,r)=>(e[r.transactionType]=(e[r.transactionType]||0)+1,e),{});return{summary:{totalTransactions:t.length,transactionsByType:a},transactions:t.map(e=>({id:e.id,transactionType:e.transactionType,amount:e.amount,description:e.description,timestamp:e.timestamp,performedBy:`${e.performedByUser.firstName} ${e.performedByUser.lastName}`})),periodStart:e,periodEnd:r}}let f=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/reports/route",pathname:"/api/reports",filename:"route",bundlePath:"app/api/reports/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\reports\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:x,workUnitAsyncStorage:v,serverHooks:N}=f;function R(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:v})}},2909:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>d,j2:()=>u});var a=t(8643),s=t(189),n=t(6467),i=t(5069),o=t(5663);let{handlers:d,auth:u,signIn:c,signOut:p}=(0,a.Ay)({adapter:(0,n.y)(i.z),session:{strategy:"jwt",maxAge:1800},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"},mfaCode:{label:"MFA Code",type:"text"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await i.z.adminUser.findUnique({where:{email:e.email}});if(!r||!r.isActive||!await o.Ay.compare(e.password,r.passwordHash))return null;return r.mfaEnabled&&!e.mfaCode&&console.warn("MFA required but not implemented yet"),await i.z.adminUser.update({where:{id:r.id},data:{lastLogin:new Date}}),await i.z.auditLog.create({data:{userId:r.id,action:"LOGIN",resourceType:"AUTH",resourceId:r.id,newValues:{timestamp:new Date,success:!0},ipAddress:"127.0.0.1",userAgent:"Unknown"}}),{id:r.id,email:r.email,name:`${r.firstName} ${r.lastName}`,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.id=r.id),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.role=r.role),e),async authorized({auth:e,request:{nextUrl:r}}){let t=!!e?.user,a=r.pathname.startsWith("/dashboard"),s=r.pathname.startsWith("/auth");return a?!!t:!s||!t||Response.redirect(new URL("/dashboard",r))}},events:{async signOut({token:e}){e?.id&&await i.z.auditLog.create({data:{userId:e.id,action:"LOGOUT",resourceType:"AUTH",resourceId:e.id,newValues:{timestamp:new Date},ipAddress:"127.0.0.1",userAgent:"Unknown"}})}}}),{GET:l,POST:m}=d},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,r,t)=>{"use strict";t.d(r,{db:()=>n,z:()=>s});var a=t(6330);let s=globalThis.prisma??new a.PrismaClient({log:["query","error","warn"]}),n=s},5511:e=>{"use strict";e.exports=require("crypto")},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,199,828,697],()=>t(1390));module.exports=a})();