import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { MessagingManager, createMessagingManager } from 'crm-shared-types';

// Initialize messaging manager
const messagingManager = createMessagingManager();

// GET - Get messages in a thread
export async function GET(
  request: NextRequest,
  { params }: { params: { threadId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      }, { status: 401 });
    }

    const { threadId } = params;
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const beforeMessageId = url.searchParams.get('before');

    const messages = messagingManager.getThreadMessages(
      threadId,
      session.user.id,
      {
        limit,
        offset,
        beforeMessageId: beforeMessageId || undefined,
      }
    );

    return NextResponse.json({
      success: true,
      data: messages,
    });
  } catch (error) {
    console.error('Failed to get thread messages:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'MESSAGES_FETCH_FAILED',
        message: error instanceof Error ? error.message : 'Failed to fetch messages',
      },
    }, { status: 500 });
  }
}

// POST - Send message to thread
export async function POST(
  request: NextRequest,
  { params }: { params: { threadId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      }, { status: 401 });
    }

    const { threadId } = params;
    const body = await request.json();
    const { content, attachments, replyToId } = body;

    if (!content || content.trim().length === 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Message content is required',
        },
      }, { status: 400 });
    }

    const messageId = messagingManager.sendMessage(
      threadId,
      session.user.id,
      session.user.name || session.user.email,
      session.user.role || 'staff',
      content,
      {
        attachments: attachments || [],
        replyToId,
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        messageId,
        message: 'Message sent successfully',
      },
    });
  } catch (error) {
    console.error('Failed to send message:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'MESSAGE_SEND_FAILED',
        message: error instanceof Error ? error.message : 'Failed to send message',
      },
    }, { status: 500 });
  }
}

// PATCH - Update thread (archive, mark as read, etc.)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { threadId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      }, { status: 401 });
    }

    const { threadId } = params;
    const body = await request.json();
    const { action, archived, participantToAdd, participantToRemove } = body;

    switch (action) {
      case 'markAsRead':
        messagingManager.markThreadAsRead(threadId, session.user.id);
        break;

      case 'archive':
        messagingManager.setThreadArchived(threadId, session.user.id, archived);
        break;

      case 'addParticipant':
        if (!participantToAdd) {
          return NextResponse.json({
            success: false,
            error: {
              code: 'INVALID_REQUEST',
              message: 'Participant information is required',
            },
          }, { status: 400 });
        }
        messagingManager.addParticipant(threadId, participantToAdd, session.user.id);
        break;

      case 'removeParticipant':
        if (!participantToRemove) {
          return NextResponse.json({
            success: false,
            error: {
              code: 'INVALID_REQUEST',
              message: 'Participant ID is required',
            },
          }, { status: 400 });
        }
        messagingManager.removeParticipant(threadId, participantToRemove, session.user.id);
        break;

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_REQUEST',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: 'Thread updated successfully',
    });
  } catch (error) {
    console.error('Failed to update thread:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'THREAD_UPDATE_FAILED',
        message: error instanceof Error ? error.message : 'Failed to update thread',
      },
    }, { status: 500 });
  }
}
