(()=>{var e={};e.id=105,e.ids=[105],e.modules={559:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\dashboard\\page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1777:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3332:(e,s,t)=>{Promise.resolve().then(t.bind(t,559))},3535:(e,s,t)=>{Promise.resolve().then(t.bind(t,2175))},3783:(e,s,t)=>{Promise.resolve().then(t.bind(t,9208))},3873:e=>{"use strict";e.exports=require("path")},3928:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},4431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,metadata:()=>n});var r=t(7413),a=t(2175);t(5692);let n={title:"Admin Portal - Innovative Centre CRM",description:"Enhanced security admin portal for financial data management"};function i({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:"min-h-screen bg-gray-50",children:(0,r.jsx)(a.SessionProvider,{children:(0,r.jsx)("div",{className:"flex min-h-screen",children:(0,r.jsx)("main",{className:"flex-1",children:e})})})})})}},4493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i});var r=t(687);t(3210);var a=t(4780);function n({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function i({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function d({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...s})}function l({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...s})}function c({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...s})}},4780:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n});var r=t(9384),a=t(2348);function n(...e){return(0,a.QP)((0,r.$)(e))}},5090:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(687),a=t(9208),n=t(6189),i=t(2688);let d=(0,i.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);var l=t(9891),c=t(3928);let o=(0,i.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),m=(0,i.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),x=(0,i.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var u=t(9523),h=t(4493);function v(){let{data:e,status:s}=(0,a.wV)(),t=(0,n.useRouter)();return"loading"===s?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading..."})]})}):e?(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsxs)("div",{className:"mb-8 flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Admin Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Enhanced security portal for financial data management"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Welcome back, ",e.user?.name," (",e.user?.role,")"]})]}),(0,r.jsxs)(u.$,{onClick:()=>{(0,a.CI)({callbackUrl:"/auth/signin"})},variant:"outline",className:"flex items-center gap-2",children:[(0,r.jsx)(d,{className:"h-4 w-4"}),"Sign Out"]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(l.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,r.jsx)("span",{className:"text-blue-800 font-medium",children:"Enhanced Security Mode Active"})]}),(0,r.jsx)("p",{className:"text-blue-700 text-sm mt-1",children:"All actions are logged and monitored. MFA is required for sensitive operations."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)(h.Zp,{children:(0,r.jsx)(h.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 text-green-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"$0"})]})]})})}),(0,r.jsx)(h.Zp,{children:(0,r.jsx)(h.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Users"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})}),(0,r.jsx)(h.Zp,{children:(0,r.jsx)(h.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m,{className:"h-8 w-8 text-purple-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Payment Records"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})}),(0,r.jsx)(h.Zp,{children:(0,r.jsx)(h.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x,{className:"h-8 w-8 text-orange-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Reports Generated"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Payment Management"}),(0,r.jsx)(h.BT,{children:"Record and track payment information"})]}),(0,r.jsx)(h.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",onClick:()=>t.push("/dashboard/payments"),children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Record New Payment"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Add payment records for students"})]})}),(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",onClick:()=>t.push("/dashboard/payments"),children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Verify Payments"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Review and verify payment records"})]})}),(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Fee Structures"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Manage course fees and pricing"})]})})]})})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"User Management"}),(0,r.jsx)(h.BT,{children:"Manage users across all services"})]}),(0,r.jsx)(h.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Staff Users"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Manage staff service users"})]})}),(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Student Users"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Manage student service users"})]})}),(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Admin Users"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Manage admin service users"})]})})]})})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Financial Reports"}),(0,r.jsx)(h.BT,{children:"Generate and view financial reports"})]}),(0,r.jsx)(h.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Revenue Report"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Monthly and yearly revenue analysis"})]})}),(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Payment Summary"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Payment status and trends"})]})}),(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Audit Trail"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"View system audit logs"})]})})]})})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"System Configuration"}),(0,r.jsx)(h.BT,{children:"Configure system settings and security"})]}),(0,r.jsx)(h.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Security Settings"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"MFA, session timeout, access control"})]})}),(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"System Configuration"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Business rules and system settings"})]})}),(0,r.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Backup & Recovery"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Data backup and recovery options"})]})})]})})]})]})]}):(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{className:"text-gray-600",children:"Please sign in to access the admin dashboard."})})})}},5692:()=>{},6189:(e,s,t)=>{"use strict";var r=t(5773);t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})},6484:(e,s,t)=>{Promise.resolve().then(t.bind(t,5090))},7016:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=t(5239),a=t(8088),n=t(8170),i=t.n(n),d=t(893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);t.d(s,l);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,559)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\dashboard\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8225:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9523:(e,s,t)=>{"use strict";t.d(s,{$:()=>l});var r=t(687);t(3210);var a=t(1391),n=t(4224),i=t(4780);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:s,size:t,asChild:n=!1,...l}){let c=n?a.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:s,size:t,className:e})),...l})}},9891:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,542,108],()=>t(7016));module.exports=r})();