import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

// Webhook receiver for incoming events from other services
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { event, subscription } = body;

    // Verify webhook signature if secret is provided
    const signature = request.headers.get('X-Webhook-Signature');
    if (process.env.WEBHOOK_SECRET && signature) {
      // In a real implementation, verify the signature
      // For now, we'll just log it
      console.log('Webhook signature received:', signature);
    }

    // Process the webhook event
    await processWebhookEvent(event);

    return NextResponse.json({
      success: true,
      message: 'Webhook processed successfully',
    });
  } catch (error) {
    console.error('Webhook processing failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Webhook processing failed',
    }, { status: 500 });
  }
}

async function processWebhookEvent(event: any) {
  const { type, data } = event;

  switch (type) {
    case 'payment.created':
    case 'payment.updated':
      await handlePaymentEvent(data);
      break;
    case 'student.created':
    case 'student.updated':
      await handleStudentEvent(data);
      break;
    default:
      console.log(`Unhandled webhook event type: ${type}`);
  }
}

async function handlePaymentEvent(data: any) {
  try {
    // Update local records or trigger notifications
    console.log('Processing payment event:', data);
    
    // Example: Update student enrollment status based on payment
    if (data.studentId) {
      // Find student enrollments and update status
      await db.studentEnrollment.updateMany({
        where: {
          studentId: data.studentId,
        },
        data: {
          paymentStatus: data.status === 'verified' ? 'paid' : 'pending',
          updatedAt: new Date(),
        },
      });
    }
  } catch (error) {
    console.error('Failed to handle payment event:', error);
    throw error;
  }
}

async function handleStudentEvent(data: any) {
  try {
    // Sync student information
    console.log('Processing student event:', data);
    
    // Example: Update local student cache or trigger notifications
    // This could involve updating teacher assignments, group memberships, etc.
    
  } catch (error) {
    console.error('Failed to handle student event:', error);
    throw error;
  }
}
