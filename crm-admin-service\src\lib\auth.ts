// NextAuth.js v5 Configuration for Admin Service
// Enhanced security for financial data management

import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { prisma } from "./db";
import bcrypt from "bcryptjs";
import { AdminRole } from "@prisma/client";

const nextAuth = NextAuth({
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: "jwt",
    maxAge: 30 * 60, // 30 minutes for enhanced security
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        mfaCode: { label: "MFA Code", type: "text" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Find admin user
          const adminUser = await prisma.adminUser.findUnique({
            where: { email: credentials.email as string },
          });

          if (!adminUser || !adminUser.isActive) {
            return null;
          }

          // Verify password
          const isValidPassword = await bcrypt.compare(
            credentials.password as string,
            adminUser.passwordHash
          );

          if (!isValidPassword) {
            return null;
          }

          // TODO: Implement MFA verification
          // For now, we'll skip MFA but log the requirement
          if (adminUser.mfaEnabled && !credentials.mfaCode) {
            console.warn("MFA required but not implemented yet");
          }

          // Update last login
          await prisma.adminUser.update({
            where: { id: adminUser.id },
            data: { lastLogin: new Date() },
          });

          // Log successful login
          await prisma.auditLog.create({
            data: {
              userId: adminUser.id,
              action: "LOGIN",
              resourceType: "AUTH",
              resourceId: adminUser.id,
              newValues: { timestamp: new Date(), success: true },
              ipAddress: "127.0.0.1", // TODO: Get real IP
              userAgent: "Unknown", // TODO: Get real user agent
            },
          });

          return {
            id: adminUser.id,
            email: adminUser.email,
            name: `${adminUser.firstName} ${adminUser.lastName}`,
            role: adminUser.role,
          };
        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as AdminRole;
      }
      return session;
    },
    async authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isOnDashboard = nextUrl.pathname.startsWith("/dashboard");
      const isOnAuth = nextUrl.pathname.startsWith("/auth");

      if (isOnDashboard) {
        if (isLoggedIn) return true;
        return false; // Redirect unauthenticated users to login page
      } else if (isOnAuth) {
        if (isLoggedIn) return Response.redirect(new URL("/dashboard", nextUrl));
        return true;
      }

      return true;
    },
  },
  events: {
    async signOut({ token }) {
      if (token?.id) {
        // Log sign out
        await prisma.auditLog.create({
          data: {
            userId: token.id as string,
            action: "LOGOUT",
            resourceType: "AUTH",
            resourceId: token.id as string,
            newValues: { timestamp: new Date() },
            ipAddress: "127.0.0.1", // TODO: Get real IP
            userAgent: "Unknown", // TODO: Get real user agent
          },
        });
      }
    },
  },
});

export const { handlers, auth, signIn, signOut } = nextAuth;
export const { GET, POST } = handlers;
