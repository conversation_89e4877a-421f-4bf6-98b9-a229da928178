(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{187:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var a=r(5155),s=r(2115),i=r(5493),n=r(5695),l=r(5525),d=r(9946);let o=(0,d.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),c=(0,d.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var u=r(2657),m=r(285),x=r(2523);r(7650);var p=r(4624),v=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,p.TL)(`Primitive.${t}`),i=s.forwardRef((e,s)=>{let{asChild:i,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?r:t,{...n,ref:s})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),g=s.forwardRef((e,t)=>(0,a.jsx)(v.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));g.displayName="Label";var h=r(2085),f=r(9434);let b=(0,h.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),y=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(g,{ref:t,className:(0,f.cn)(b(),r),...s})});y.displayName=g.displayName;let j=(0,h.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function w(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,f.cn)(j({variant:r}),t),...s})}function N(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,f.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}function k(){let[e,t]=(0,s.useState)(""),[r,d]=(0,s.useState)(""),[p,v]=(0,s.useState)(""),[g,h]=(0,s.useState)(!1),[f,b]=(0,s.useState)(!1),[j,k]=(0,s.useState)(""),A=(0,n.useRouter)(),C=async t=>{t.preventDefault(),b(!0),k("");try{let t=await (0,i.Jv)("credentials",{email:e,password:r,mfaCode:p,redirect:!1});(null==t?void 0:t.error)?k("Invalid credentials. Please try again."):await (0,i.Ht)()?A.push("/dashboard"):k("Authentication failed. Please try again.")}catch(e){k("An error occurred during sign in. Please try again.")}finally{b(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100",children:(0,a.jsx)(l.A,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Admin Portal Sign In"}),(0,a.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Enhanced security portal for financial data management"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,a.jsx)("span",{className:"text-blue-800 font-medium",children:"Enhanced Security"})]}),(0,a.jsx)("p",{className:"text-blue-700 text-sm mt-1",children:"All login attempts are monitored and logged for security purposes."})]}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:C,children:[j&&(0,a.jsxs)(w,{variant:"destructive",children:[(0,a.jsx)(o,{className:"h-4 w-4"}),(0,a.jsx)(N,{children:j})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(y,{htmlFor:"email",children:"Email Address"}),(0,a.jsx)(x.p,{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"mt-1",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(y,{htmlFor:"password",children:"Password"}),(0,a.jsxs)("div",{className:"relative mt-1",children:[(0,a.jsx)(x.p,{id:"password",name:"password",type:g?"text":"password",autoComplete:"current-password",required:!0,value:r,onChange:e=>d(e.target.value),placeholder:"Enter your password"}),(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>h(!g),children:g?(0,a.jsx)(c,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(u.A,{className:"h-4 w-4 text-gray-400"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(y,{htmlFor:"mfaCode",children:"MFA Code (Optional)"}),(0,a.jsx)(x.p,{id:"mfaCode",name:"mfaCode",type:"text",value:p,onChange:e=>v(e.target.value),className:"mt-1",placeholder:"Enter 6-digit MFA code",maxLength:6}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Multi-factor authentication will be required in production"})]})]}),(0,a.jsx)("div",{children:(0,a.jsx)(m.$,{type:"submit",className:"w-full",disabled:f,children:f?"Signing in...":"Sign In"})})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"This is a secure admin portal. Unauthorized access is prohibited."})})]})})}},285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(5155);r(2115);var s=r(4624),i=r(2085),n=r(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:i,asChild:d=!1,...o}=e,c=d?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:r,size:i,className:t})),...o})}},1192:(e,t,r)=>{Promise.resolve().then(r.bind(r,187))},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(5155),s=r(2115),i=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});n.displayName="Input"},2657:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5525:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var a=r(2596),s=r(9688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[493,497,441,684,358],()=>t(1192)),_N_E=e.O()}]);