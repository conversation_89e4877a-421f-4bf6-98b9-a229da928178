import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

// Webhook receiver for incoming events from other services
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { event } = body;

    // Verify webhook signature if secret is provided
    const signature = request.headers.get('X-Webhook-Signature');
    if (process.env.WEBHOOK_SECRET && signature) {
      // In a real implementation, verify the signature
      console.log('Webhook signature received:', signature);
    }

    // Process the webhook event
    await processWebhookEvent(event);

    return NextResponse.json({
      success: true,
      message: 'Webhook processed successfully',
    });
  } catch (error) {
    console.error('Webhook processing failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Webhook processing failed',
    }, { status: 500 });
  }
}

async function processWebhookEvent(event: any) {
  const { type, data } = event;

  switch (type) {
    case 'lead.created':
    case 'lead.updated':
      await handleLeadEvent(data);
      break;
    case 'student.created':
    case 'student.updated':
      await handleStudentEvent(data);
      break;
    case 'course.created':
    case 'course.updated':
      await handleCourseEvent(data);
      break;
    default:
      console.log(`Unhandled webhook event type: ${type}`);
  }
}

async function handleLeadEvent(data: any) {
  try {
    // Create audit log for lead activity
    await db.auditLog.create({
      data: {
        action: 'lead_activity',
        entityType: 'lead',
        entityId: data.id,
        details: {
          leadData: data,
          source: 'staff_service_webhook',
        },
        performedBy: 'system',
        performedAt: new Date(),
      },
    });

    console.log('Lead event processed and logged:', data.id);
  } catch (error) {
    console.error('Failed to handle lead event:', error);
    throw error;
  }
}

async function handleStudentEvent(data: any) {
  try {
    // Create audit log for student activity
    await db.auditLog.create({
      data: {
        action: 'student_activity',
        entityType: 'student',
        entityId: data.id,
        details: {
          studentData: data,
          source: 'service_webhook',
        },
        performedBy: 'system',
        performedAt: new Date(),
      },
    });

    console.log('Student event processed and logged:', data.id);
  } catch (error) {
    console.error('Failed to handle student event:', error);
    throw error;
  }
}

async function handleCourseEvent(data: any) {
  try {
    // Create audit log for course activity
    await db.auditLog.create({
      data: {
        action: 'course_activity',
        entityType: 'course',
        entityId: data.id,
        details: {
          courseData: data,
          source: 'staff_service_webhook',
        },
        performedBy: 'system',
        performedAt: new Date(),
      },
    });

    console.log('Course event processed and logged:', data.id);
  } catch (error) {
    console.error('Failed to handle course event:', error);
    throw error;
  }
}
