// Messaging System for User-to-User Communication

export interface MessageThread {
  id: string;
  subject: string;
  participants: string[];
  participantDetails: Array<{
    userId: string;
    name: string;
    role: string;
    service: string;
  }>;
  lastMessageAt: Date;
  lastMessage?: string;
  unreadCount: Record<string, number>;
  isArchived: boolean;
  tags: string[];
  priority: 'low' | 'normal' | 'high' | 'urgent';
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Message {
  id: string;
  threadId: string;
  senderId: string;
  senderName: string;
  senderRole: string;
  content: string;
  attachments: Array<{
    id: string;
    filename: string;
    size: number;
    mimeType: string;
    url: string;
  }>;
  readBy: Array<{
    userId: string;
    readAt: Date;
  }>;
  isEdited: boolean;
  editedAt?: Date;
  replyToId?: string;
  sentAt: Date;
  createdAt: Date;
}

export interface MessageFilter {
  userId?: string;
  unreadOnly?: boolean;
  archived?: boolean;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  tags?: string[];
  dateFrom?: Date;
  dateTo?: Date;
  searchQuery?: string;
}

// Messaging Manager
export class MessagingManager {
  private threads: Map<string, MessageThread> = new Map();
  private messages: Map<string, Message> = new Map();
  private userSubscriptions: Map<string, Set<string>> = new Map(); // userId -> threadIds

  // Thread Management
  createThread(
    subject: string,
    participants: Array<{
      userId: string;
      name: string;
      role: string;
      service: string;
    }>,
    createdBy: string,
    options: {
      priority?: 'low' | 'normal' | 'high' | 'urgent';
      tags?: string[];
    } = {}
  ): string {
    const threadId = this.generateId();
    const thread: MessageThread = {
      id: threadId,
      subject,
      participants: participants.map(p => p.userId),
      participantDetails: participants,
      lastMessageAt: new Date(),
      unreadCount: {},
      isArchived: false,
      tags: options.tags || [],
      priority: options.priority || 'normal',
      createdBy,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Initialize unread count for all participants
    participants.forEach(p => {
      thread.unreadCount[p.userId] = 0;
    });

    this.threads.set(threadId, thread);

    // Subscribe participants to the thread
    participants.forEach(p => {
      this.subscribeUserToThread(p.userId, threadId);
    });

    return threadId;
  }

  // Send message
  sendMessage(
    threadId: string,
    senderId: string,
    senderName: string,
    senderRole: string,
    content: string,
    options: {
      attachments?: Array<{
        filename: string;
        size: number;
        mimeType: string;
        url: string;
      }>;
      replyToId?: string;
    } = {}
  ): string {
    const thread = this.threads.get(threadId);
    if (!thread) {
      throw new Error(`Thread ${threadId} not found`);
    }

    if (!thread.participants.includes(senderId)) {
      throw new Error('User is not a participant in this thread');
    }

    const messageId = this.generateId();
    const message: Message = {
      id: messageId,
      threadId,
      senderId,
      senderName,
      senderRole,
      content,
      attachments: options.attachments?.map(att => ({
        id: this.generateId(),
        ...att,
      })) || [],
      readBy: [{
        userId: senderId,
        readAt: new Date(),
      }],
      isEdited: false,
      replyToId: options.replyToId,
      sentAt: new Date(),
      createdAt: new Date(),
    };

    this.messages.set(messageId, message);

    // Update thread
    thread.lastMessageAt = new Date();
    thread.lastMessage = content.substring(0, 100) + (content.length > 100 ? '...' : '');
    thread.updatedAt = new Date();

    // Update unread count for other participants
    thread.participants.forEach(participantId => {
      if (participantId !== senderId) {
        thread.unreadCount[participantId] = (thread.unreadCount[participantId] || 0) + 1;
      }
    });

    return messageId;
  }

  // Mark message as read
  markMessageAsRead(messageId: string, userId: string): void {
    const message = this.messages.get(messageId);
    if (!message) {
      throw new Error(`Message ${messageId} not found`);
    }

    const thread = this.threads.get(message.threadId);
    if (!thread || !thread.participants.includes(userId)) {
      throw new Error('User is not a participant in this thread');
    }

    // Add to read list if not already read
    if (!message.readBy.some(r => r.userId === userId)) {
      message.readBy.push({
        userId,
        readAt: new Date(),
      });

      // Decrease unread count
      if (thread.unreadCount[userId] > 0) {
        thread.unreadCount[userId]--;
      }
    }
  }

  // Mark all messages in thread as read
  markThreadAsRead(threadId: string, userId: string): void {
    const thread = this.threads.get(threadId);
    if (!thread || !thread.participants.includes(userId)) {
      throw new Error('User is not a participant in this thread');
    }

    // Get all unread messages in thread
    const threadMessages = Array.from(this.messages.values())
      .filter(m => m.threadId === threadId && !m.readBy.some(r => r.userId === userId));

    threadMessages.forEach(message => {
      message.readBy.push({
        userId,
        readAt: new Date(),
      });
    });

    // Reset unread count
    thread.unreadCount[userId] = 0;
  }

  // Get user threads
  getUserThreads(userId: string, filter: MessageFilter = {}): MessageThread[] {
    const userThreadIds = this.userSubscriptions.get(userId) || new Set();
    let threads = Array.from(userThreadIds)
      .map(threadId => this.threads.get(threadId))
      .filter((thread): thread is MessageThread => thread !== undefined);

    // Apply filters
    if (filter.unreadOnly) {
      threads = threads.filter(thread => (thread.unreadCount[userId] || 0) > 0);
    }

    if (filter.archived !== undefined) {
      threads = threads.filter(thread => thread.isArchived === filter.archived);
    }

    if (filter.priority) {
      threads = threads.filter(thread => thread.priority === filter.priority);
    }

    if (filter.tags && filter.tags.length > 0) {
      threads = threads.filter(thread => 
        filter.tags!.some(tag => thread.tags.includes(tag))
      );
    }

    if (filter.searchQuery) {
      const query = filter.searchQuery.toLowerCase();
      threads = threads.filter(thread => 
        thread.subject.toLowerCase().includes(query) ||
        (thread.lastMessage && thread.lastMessage.toLowerCase().includes(query))
      );
    }

    // Sort by last message date
    return threads.sort((a, b) => b.lastMessageAt.getTime() - a.lastMessageAt.getTime());
  }

  // Get thread messages
  getThreadMessages(threadId: string, userId: string, options: {
    limit?: number;
    offset?: number;
    beforeMessageId?: string;
  } = {}): Message[] {
    const thread = this.threads.get(threadId);
    if (!thread || !thread.participants.includes(userId)) {
      throw new Error('User is not a participant in this thread');
    }

    let messages = Array.from(this.messages.values())
      .filter(m => m.threadId === threadId)
      .sort((a, b) => a.sentAt.getTime() - b.sentAt.getTime());

    if (options.beforeMessageId) {
      const beforeMessage = this.messages.get(options.beforeMessageId);
      if (beforeMessage) {
        messages = messages.filter(m => m.sentAt < beforeMessage.sentAt);
      }
    }

    if (options.offset) {
      messages = messages.slice(options.offset);
    }

    if (options.limit) {
      messages = messages.slice(0, options.limit);
    }

    return messages;
  }

  // Archive/Unarchive thread
  setThreadArchived(threadId: string, userId: string, archived: boolean): void {
    const thread = this.threads.get(threadId);
    if (!thread || !thread.participants.includes(userId)) {
      throw new Error('User is not a participant in this thread');
    }

    thread.isArchived = archived;
    thread.updatedAt = new Date();
  }

  // Add participant to thread
  addParticipant(
    threadId: string,
    newParticipant: {
      userId: string;
      name: string;
      role: string;
      service: string;
    },
    addedBy: string
  ): void {
    const thread = this.threads.get(threadId);
    if (!thread || !thread.participants.includes(addedBy)) {
      throw new Error('User is not authorized to add participants');
    }

    if (!thread.participants.includes(newParticipant.userId)) {
      thread.participants.push(newParticipant.userId);
      thread.participantDetails.push(newParticipant);
      thread.unreadCount[newParticipant.userId] = 0;
      thread.updatedAt = new Date();

      this.subscribeUserToThread(newParticipant.userId, threadId);

      // Send system message about participant addition
      this.sendMessage(
        threadId,
        'system',
        'System',
        'system',
        `${newParticipant.name} was added to the conversation by ${addedBy}`,
      );
    }
  }

  // Remove participant from thread
  removeParticipant(threadId: string, participantId: string, removedBy: string): void {
    const thread = this.threads.get(threadId);
    if (!thread || !thread.participants.includes(removedBy)) {
      throw new Error('User is not authorized to remove participants');
    }

    const participantIndex = thread.participants.indexOf(participantId);
    if (participantIndex > -1) {
      thread.participants.splice(participantIndex, 1);
      thread.participantDetails = thread.participantDetails.filter(p => p.userId !== participantId);
      delete thread.unreadCount[participantId];
      thread.updatedAt = new Date();

      this.unsubscribeUserFromThread(participantId, threadId);

      // Send system message about participant removal
      const removedParticipant = thread.participantDetails.find(p => p.userId === participantId);
      this.sendMessage(
        threadId,
        'system',
        'System',
        'system',
        `${removedParticipant?.name || participantId} was removed from the conversation`,
      );
    }
  }

  // Subscribe user to thread
  private subscribeUserToThread(userId: string, threadId: string): void {
    if (!this.userSubscriptions.has(userId)) {
      this.userSubscriptions.set(userId, new Set());
    }
    this.userSubscriptions.get(userId)!.add(threadId);
  }

  // Unsubscribe user from thread
  private unsubscribeUserFromThread(userId: string, threadId: string): void {
    const userThreads = this.userSubscriptions.get(userId);
    if (userThreads) {
      userThreads.delete(threadId);
    }
  }

  // Generate unique ID
  private generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get messaging statistics
  getMessagingStats(userId: string): {
    totalThreads: number;
    unreadThreads: number;
    totalMessages: number;
    unreadMessages: number;
  } {
    const userThreadIds = this.userSubscriptions.get(userId) || new Set();
    const userThreads = Array.from(userThreadIds)
      .map(threadId => this.threads.get(threadId))
      .filter((thread): thread is MessageThread => thread !== undefined);

    const unreadThreads = userThreads.filter(thread => (thread.unreadCount[userId] || 0) > 0);
    const totalUnreadMessages = userThreads.reduce((sum, thread) => sum + (thread.unreadCount[userId] || 0), 0);

    const userMessages = Array.from(this.messages.values())
      .filter(message => 
        userThreads.some(thread => thread.id === message.threadId)
      );

    return {
      totalThreads: userThreads.length,
      unreadThreads: unreadThreads.length,
      totalMessages: userMessages.length,
      unreadMessages: totalUnreadMessages,
    };
  }
}

// Export factory function
export function createMessagingManager(): MessagingManager {
  return new MessagingManager();
}
