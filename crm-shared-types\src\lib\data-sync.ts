// Data Synchronization Service for Inter-Service Communication

import { ServiceClientFactory } from './service-clients';

export interface SyncEvent {
  id: string;
  type: 'create' | 'update' | 'delete';
  entity: 'lead' | 'student' | 'course' | 'group' | 'payment' | 'assignment';
  entityId: string;
  data: any;
  sourceService: string;
  timestamp: Date;
  version: number;
}

export interface SyncResult {
  success: boolean;
  syncedServices: string[];
  errors: Array<{
    service: string;
    error: string;
  }>;
}

// Data synchronization manager
export class DataSyncManager {
  private serviceFactory: ServiceClientFactory;
  private syncQueue: SyncEvent[] = [];
  private isProcessing = false;

  constructor(serviceFactory: ServiceClientFactory) {
    this.serviceFactory = serviceFactory;
  }

  // Queue a sync event
  async queueSync(event: Omit<SyncEvent, 'id' | 'timestamp'>): Promise<void> {
    const syncEvent: SyncEvent = {
      ...event,
      id: this.generateSyncId(),
      timestamp: new Date(),
    };

    this.syncQueue.push(syncEvent);
    
    // Process queue if not already processing
    if (!this.isProcessing) {
      await this.processQueue();
    }
  }

  // Process the sync queue
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.syncQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      while (this.syncQueue.length > 0) {
        const event = this.syncQueue.shift();
        if (event) {
          await this.processSyncEvent(event);
        }
      }
    } finally {
      this.isProcessing = false;
    }
  }

  // Process individual sync event
  private async processSyncEvent(event: SyncEvent): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      syncedServices: [],
      errors: [],
    };

    try {
      switch (event.entity) {
        case 'lead':
          await this.syncLead(event, result);
          break;
        case 'student':
          await this.syncStudent(event, result);
          break;
        case 'course':
          await this.syncCourse(event, result);
          break;
        case 'group':
          await this.syncGroup(event, result);
          break;
        case 'payment':
          await this.syncPayment(event, result);
          break;
        case 'assignment':
          await this.syncAssignment(event, result);
          break;
        default:
          throw new Error(`Unknown entity type: ${event.entity}`);
      }
    } catch (error) {
      result.success = false;
      result.errors.push({
        service: 'sync-manager',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return result;
  }

  // Sync lead data
  private async syncLead(event: SyncEvent, result: SyncResult): Promise<void> {
    // Leads are primarily managed by staff service
    // Sync to admin service for reporting
    if (event.sourceService !== 'crm-admin-service') {
      try {
        const adminClient = this.serviceFactory.getAdminClient();
        
        switch (event.type) {
          case 'create':
          case 'update':
            // Notify admin service of lead activity for reporting
            await adminClient.post('/api/internal/leads/sync', {
              leadId: event.entityId,
              data: event.data,
              action: event.type,
            });
            result.syncedServices.push('crm-admin-service');
            break;
        }
      } catch (error) {
        result.errors.push({
          service: 'crm-admin-service',
          error: error instanceof Error ? error.message : 'Sync failed',
        });
      }
    }
  }

  // Sync student data
  private async syncStudent(event: SyncEvent, result: SyncResult): Promise<void> {
    // Students are managed across all services
    const targetServices = ['crm-admin-service', 'crm-staff-service', 'crm-student-service']
      .filter(service => service !== event.sourceService);

    for (const service of targetServices) {
      try {
        const client = this.getClientForService(service);
        
        switch (event.type) {
          case 'create':
          case 'update':
            await client.post('/api/internal/students/sync', {
              studentId: event.entityId,
              data: event.data,
              action: event.type,
            });
            result.syncedServices.push(service);
            break;
        }
      } catch (error) {
        result.errors.push({
          service,
          error: error instanceof Error ? error.message : 'Sync failed',
        });
      }
    }
  }

  // Sync course data
  private async syncCourse(event: SyncEvent, result: SyncResult): Promise<void> {
    // Courses are created in staff service, synced to student service
    if (event.sourceService === 'crm-staff-service') {
      try {
        const studentClient = this.serviceFactory.getStudentClient();
        
        switch (event.type) {
          case 'create':
          case 'update':
            await studentClient.post('/api/internal/courses/sync', {
              courseId: event.entityId,
              data: event.data,
              action: event.type,
            });
            result.syncedServices.push('crm-student-service');
            break;
        }
      } catch (error) {
        result.errors.push({
          service: 'crm-student-service',
          error: error instanceof Error ? error.message : 'Sync failed',
        });
      }
    }
  }

  // Sync group data
  private async syncGroup(event: SyncEvent, result: SyncResult): Promise<void> {
    // Groups are created in staff service, synced to student service for schedules
    if (event.sourceService === 'crm-staff-service') {
      try {
        const studentClient = this.serviceFactory.getStudentClient();
        
        switch (event.type) {
          case 'create':
          case 'update':
            await studentClient.post('/api/schedule/sync', {
              groupId: event.entityId,
              data: event.data,
              action: event.type,
            });
            result.syncedServices.push('crm-student-service');
            break;
        }
      } catch (error) {
        result.errors.push({
          service: 'crm-student-service',
          error: error instanceof Error ? error.message : 'Sync failed',
        });
      }
    }
  }

  // Sync payment data
  private async syncPayment(event: SyncEvent, result: SyncResult): Promise<void> {
    // Payments are managed in admin service, synced to staff service for reporting
    if (event.sourceService === 'crm-admin-service') {
      try {
        const staffClient = this.serviceFactory.getStaffClient();
        
        switch (event.type) {
          case 'create':
          case 'update':
            await staffClient.post('/api/internal/payments/sync', {
              paymentId: event.entityId,
              data: event.data,
              action: event.type,
            });
            result.syncedServices.push('crm-staff-service');
            break;
        }
      } catch (error) {
        result.errors.push({
          service: 'crm-staff-service',
          error: error instanceof Error ? error.message : 'Sync failed',
        });
      }
    }
  }

  // Sync assignment data
  private async syncAssignment(event: SyncEvent, result: SyncResult): Promise<void> {
    // Assignments are created in staff service, synced to student service
    if (event.sourceService === 'crm-staff-service') {
      try {
        const studentClient = this.serviceFactory.getStudentClient();
        
        switch (event.type) {
          case 'create':
          case 'update':
            await studentClient.post('/api/assignments/sync', {
              assignmentId: event.entityId,
              data: event.data,
              action: event.type,
            });
            result.syncedServices.push('crm-student-service');
            break;
        }
      } catch (error) {
        result.errors.push({
          service: 'crm-student-service',
          error: error instanceof Error ? error.message : 'Sync failed',
        });
      }
    }
  }

  // Get appropriate client for service
  private getClientForService(serviceName: string) {
    switch (serviceName) {
      case 'crm-admin-service':
        return this.serviceFactory.getAdminClient();
      case 'crm-staff-service':
        return this.serviceFactory.getStaffClient();
      case 'crm-student-service':
        return this.serviceFactory.getStudentClient();
      default:
        throw new Error(`Unknown service: ${serviceName}`);
    }
  }

  // Generate unique sync ID
  private generateSyncId(): string {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get sync queue status
  getQueueStatus(): { queueLength: number; isProcessing: boolean } {
    return {
      queueLength: this.syncQueue.length,
      isProcessing: this.isProcessing,
    };
  }
}

// Export singleton instance factory
export function createDataSyncManager(serviceFactory: ServiceClientFactory): DataSyncManager {
  return new DataSyncManager(serviceFactory);
}
