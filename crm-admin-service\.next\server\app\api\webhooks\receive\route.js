(()=>{var e={};e.id=126,e.ids=[126],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1527:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>y,serverHooks:()=>w,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{POST:()=>d});var o=r(6559),a=r(8088),n=r(7719),i=r(2190),c=r(5069);async function d(e){try{let{event:t}=await e.json(),r=e.headers.get("X-Webhook-Signature");return process.env.WEBHOOK_SECRET&&r&&console.log("Webhook signature received:",r),await u(t),i.NextResponse.json({success:!0,message:"Webhook processed successfully"})}catch(e){return console.error("Webhook processing failed:",e),i.NextResponse.json({success:!1,error:"Webhook processing failed"},{status:500})}}async function u(e){let{type:t,data:r}=e;switch(t){case"lead.created":case"lead.updated":await p(r);break;case"student.created":case"student.updated":await l(r);break;case"course.created":case"course.updated":await v(r);break;default:console.log(`Unhandled webhook event type: ${t}`)}}async function p(e){try{await c.db.auditLog.create({data:{action:"lead_activity",entityType:"lead",entityId:e.id,details:{leadData:e,source:"staff_service_webhook"},performedBy:"system",performedAt:new Date}}),console.log("Lead event processed and logged:",e.id)}catch(e){throw console.error("Failed to handle lead event:",e),e}}async function l(e){try{await c.db.auditLog.create({data:{action:"student_activity",entityType:"student",entityId:e.id,details:{studentData:e,source:"service_webhook"},performedBy:"system",performedAt:new Date}}),console.log("Student event processed and logged:",e.id)}catch(e){throw console.error("Failed to handle student event:",e),e}}async function v(e){try{await c.db.auditLog.create({data:{action:"course_activity",entityType:"course",entityId:e.id,details:{courseData:e,source:"staff_service_webhook"},performedBy:"system",performedAt:new Date}}),console.log("Course event processed and logged:",e.id)}catch(e){throw console.error("Failed to handle course event:",e),e}}let y=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/webhooks/receive/route",pathname:"/api/webhooks/receive",filename:"route",bundlePath:"app/api/webhooks/receive/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\webhooks\\receive\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:g,serverHooks:w}=y;function m(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:g})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,t,r)=>{"use strict";r.d(t,{db:()=>a,z:()=>o});var s=r(6330);let o=globalThis.prisma??new s.PrismaClient({log:["query","error","warn"]}),a=o},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,199],()=>r(1527));module.exports=s})();