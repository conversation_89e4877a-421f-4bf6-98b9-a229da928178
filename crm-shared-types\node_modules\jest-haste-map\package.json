{"name": "jest-haste-map", "version": "29.7.0", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-haste-map"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/types": "^29.6.3", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "walker": "^1.0.8"}, "devDependencies": {"@types/fb-watchman": "^2.0.0", "@types/micromatch": "^4.0.1", "slash": "^3.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630"}