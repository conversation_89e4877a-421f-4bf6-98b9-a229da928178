"use strict";exports.id=828,exports.ids=[828],exports.modules={163:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(1042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},189:(e,t,r)=>{r.d(t,{A:()=>n});function n(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},899:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1042:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,c.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,a.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(8388),a=r(2637),i=r(1846),o=r(1162),s=r(4971),c=r(8479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1162:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return i}});let n=r(8704),a=r(9026);function i(e){return(0,a.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1846:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return a}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},2584:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return a}});let n=r(3763);class a extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new a}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,a){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,a);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return n.ReflectAdapter.get(t,o,a)},set(t,r,a,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,a,i);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);return n.ReflectAdapter.set(t,s??r,a,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return a.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},2637:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},2765:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return a}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function a(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2836:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4069:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return f},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return c},appendMutableCookies:function(){return d},areCookiesMutableInCurrentPhase:function(){return h},getModifiedCookieValues:function(){return u},responseCookiesToRequestCookies:function(){return b},wrapWithMutableAccessCheck:function(){return p}});let n=r(3158),a=r(3763),i=r(9294),o=r(3033);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new s}}class c{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return a.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function u(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function d(e,t){let r=u(t);if(0===r.length)return!1;let a=new n.ResponseCookies(e),i=a.getAll();for(let e of r)a.set(e);for(let e of i)a.set(e);return!0}class f{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],s=new Set,c=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),o=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of o){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},u=new Proxy(r,{get(e,t,r){switch(t){case l:return o;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),u}finally{c()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),u}finally{c()}};default:return a.ReflectAdapter.get(e,t,r)}}});return u}}function p(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return y("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return y("cookies().set"),e.set(...r),t};default:return a.ReflectAdapter.get(e,r,n)}}});return t}function h(e){return"action"===e.phase}function y(e){if(!h((0,o.getExpectedRequestStore)(e)))throw new s}function b(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},5663:(e,t,r)=>{r.d(t,{Ay:()=>S});var n=r(5511),a=null;function i(e,t){if("number"!=typeof(e=e||m))throw Error("Illegal arguments: "+typeof e+", "+typeof t);e<4?e=4:e>31&&(e=31);var r=[];return r.push("$2b$"),e<10&&r.push("0"),r.push(e.toString()),r.push("$"),r.push(h(function(e){try{return crypto.getRandomValues(new Uint8Array(e))}catch{}try{return n.randomBytes(e)}catch{}if(!a)throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");return a(e)}(b),b)),r.join("")}function o(e,t,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof e&&(r=e,e=void 0),void 0===e)e=m;else if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);function n(t){u(function(){try{t(null,i(e))}catch(e){t(e)}})}if(!r)return new Promise(function(e,t){n(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);n(r)}function s(e,t){if(void 0===t&&(t=m),"number"==typeof t&&(t=i(t)),"string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return A(e,t)}function c(e,t,r,n){function a(r){"string"==typeof e&&"number"==typeof t?o(t,function(t,a){A(e,a,r,n)}):"string"==typeof e&&"string"==typeof t?A(e,t,r,n):u(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)))}if(!r)return new Promise(function(e,t){a(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);a(r)}function l(e,t){for(var r=e.length^t.length,n=0;n<e.length;++n)r|=e.charCodeAt(n)^t.charCodeAt(n);return 0===r}var u="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout;function d(e){for(var t=0,r=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<128?t+=1:r<2048?t+=2:(64512&r)==55296&&(64512&e.charCodeAt(n+1))==56320?(++n,t+=4):t+=3;return t}var f="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),p=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1];function h(e,t){var r,n,a=0,i=[];if(t<=0||t>e.length)throw Error("Illegal len: "+t);for(;a<t;){if(r=255&e[a++],i.push(f[r>>2&63]),r=(3&r)<<4,a>=t||(r|=(n=255&e[a++])>>4&15,i.push(f[63&r]),r=(15&n)<<2,a>=t)){i.push(f[63&r]);break}r|=(n=255&e[a++])>>6&3,i.push(f[63&r]),i.push(f[63&n])}return i.join("")}function y(e,t){var r,n,a,i,o,s=0,c=e.length,l=0,u=[];if(t<=0)throw Error("Illegal len: "+t);for(;s<c-1&&l<t&&(r=(o=e.charCodeAt(s++))<p.length?p[o]:-1,n=(o=e.charCodeAt(s++))<p.length?p[o]:-1,-1!=r&&-1!=n)&&(i=r<<2>>>0|(48&n)>>4,u.push(String.fromCharCode(i)),!(++l>=t||s>=c||-1==(a=(o=e.charCodeAt(s++))<p.length?p[o]:-1)||(i=(15&n)<<4>>>0|(60&a)>>2,u.push(String.fromCharCode(i)),++l>=t||s>=c)));){;i=(3&a)<<6>>>0|((o=e.charCodeAt(s++))<p.length?p[o]:-1),u.push(String.fromCharCode(i)),++l}var d=[];for(s=0;s<l;s++)d.push(u[s].charCodeAt(0));return d}var b=16,m=10,g=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],x=[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a,0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7,0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0,0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6],w=[0x4f727068,0x65616e42,0x65686f6c,0x64657253,0x63727944,0x6f756274];function _(e,t,r,n){var a,i=e[t],o=e[t+1];return i^=r[0],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[1],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[2],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[3],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[4],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[5],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[6],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[7],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[8],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[9],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[10],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[11],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[12],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[13],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[14],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[15],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[16],e[t]=o^r[17],e[t+1]=i,e}function v(e,t){for(var r=0,n=0;r<4;++r)n=n<<8|255&e[t],t=(t+1)%e.length;return{key:n,offp:t}}function k(e,t,r){for(var n,a=0,i=[0,0],o=t.length,s=r.length,c=0;c<o;c++)a=(n=v(e,a)).offp,t[c]=t[c]^n.key;for(c=0;c<o;c+=2)i=_(i,0,t,r),t[c]=i[0],t[c+1]=i[1];for(c=0;c<s;c+=2)i=_(i,0,t,r),r[c]=i[0],r[c+1]=i[1]}function E(e,t,r,n,a){var i,o,s=w.slice(),c=s.length;if(r<4||r>31){if(o=Error("Illegal number of rounds (4-31): "+r),n)return void u(n.bind(this,o));throw o}if(t.length!==b){if(o=Error("Illegal salt length: "+t.length+" != "+b),n)return void u(n.bind(this,o));throw o}r=1<<r>>>0;var l,d,f,p=0;function h(){if(a&&a(p/r),p<r)for(var i=Date.now();p<r&&(p+=1,k(e,l,d),k(t,l,d),!(Date.now()-i>100)););else{for(p=0;p<64;p++)for(f=0;f<c>>1;f++)_(s,f<<1,l,d);var o=[];for(p=0;p<c;p++)o.push((s[p]>>24&255)>>>0),o.push((s[p]>>16&255)>>>0),o.push((s[p]>>8&255)>>>0),o.push((255&s[p])>>>0);return n?void n(null,o):o}n&&u(h)}if("function"==typeof Int32Array?(l=new Int32Array(g),d=new Int32Array(x)):(l=g.slice(),d=x.slice()),!function(e,t,r,n){for(var a,i=0,o=[0,0],s=r.length,c=n.length,l=0;l<s;l++)i=(a=v(t,i)).offp,r[l]=r[l]^a.key;for(l=0,i=0;l<s;l+=2)i=(a=v(e,i)).offp,o[0]^=a.key,i=(a=v(e,i)).offp,o[1]^=a.key,o=_(o,0,r,n),r[l]=o[0],r[l+1]=o[1];for(l=0;l<c;l+=2)i=(a=v(e,i)).offp,o[0]^=a.key,i=(a=v(e,i)).offp,o[1]^=a.key,o=_(o,0,r,n),n[l]=o[0],n[l+1]=o[1]}(t,e,l,d),void 0!==n)h();else for(;;)if(void 0!==(i=h()))return i||[]}function A(e,t,r,n){if("string"!=typeof e||"string"!=typeof t){if(a=Error("Invalid string / salt: Not a string"),r)return void u(r.bind(this,a));throw a}if("$"!==t.charAt(0)||"2"!==t.charAt(1)){if(a=Error("Invalid salt version: "+t.substring(0,2)),r)return void u(r.bind(this,a));throw a}if("$"===t.charAt(2))i="\0",o=3;else{if("a"!==(i=t.charAt(2))&&"b"!==i&&"y"!==i||"$"!==t.charAt(3)){if(a=Error("Invalid salt revision: "+t.substring(2,4)),r)return void u(r.bind(this,a));throw a}o=4}if(t.charAt(o+2)>"$"){if(a=Error("Missing salt rounds"),r)return void u(r.bind(this,a));throw a}var a,i,o,s=10*parseInt(t.substring(o,o+1),10)+parseInt(t.substring(o+1,o+2),10),c=t.substring(o+3,o+25),l=function(e){for(var t,r,n=0,a=Array(d(e)),i=0,o=e.length;i<o;++i)(t=e.charCodeAt(i))<128?a[n++]=t:(t<2048?a[n++]=t>>6|192:((64512&t)==55296&&(64512&(r=e.charCodeAt(i+1)))==56320?(t=65536+((1023&t)<<10)+(1023&r),++i,a[n++]=t>>18|240,a[n++]=t>>12&63|128):a[n++]=t>>12|224,a[n++]=t>>6&63|128),a[n++]=63&t|128);return a}(e+=i>="a"?"\0":""),f=y(c,b);function p(e){var t=[];return t.push("$2"),i>="a"&&t.push(i),t.push("$"),s<10&&t.push("0"),t.push(s.toString()),t.push("$"),t.push(h(f,f.length)),t.push(h(e,4*w.length-1)),t.join("")}if(void 0===r)return p(E(l,f,s));E(l,f,s,function(e,t){e?r(e,null):r(null,p(t))},n)}let S={setRandomFallback:function(e){a=e},genSaltSync:i,genSalt:o,hashSync:s,hash:c,compareSync:function(e,t){if("string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return 60===t.length&&l(s(e,t.substring(0,t.length-31)),t)},compare:function(e,t,r,n){function a(r){return"string"!=typeof e||"string"!=typeof t?void u(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t))):60!==t.length?void u(r.bind(this,null,!1)):void c(e,t.substring(0,29),function(e,n){e?r(e):r(null,l(n,t))},n)}if(!r)return new Promise(function(e,t){a(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);a(r)},getRounds:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return parseInt(e.split("$")[2],10)},getSalt:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);if(60!==e.length)throw Error("Illegal hash length: "+e.length+" != 60");return e.substring(0,29)},truncates:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return d(e)>72},encodeBase64:function(e,t){return h(e,t)},decodeBase64:function(e,t){return y(e,t)}}},6280:(e,t,r)=>{Object.defineProperty(t,"b",{enumerable:!0,get:function(){return d}});let n=r(2584),a=r(9294),i=r(3033),o=r(4971),s=r(23),c=r(8388),l=r(6926),u=(r(4523),r(8719));function d(){let e=a.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,u.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return p(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,l=t;let n=f.get(l);if(n)return n;let a=(0,c.makeHangingPromise)(l.renderSignal,"`headers()`");return f.set(l,a),Object.defineProperties(a,{append:{value:function(){let e=`\`headers().append(${h(arguments[0])}, ...)\``,t=b(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},delete:{value:function(){let e=`\`headers().delete(${h(arguments[0])})\``,t=b(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},get:{value:function(){let e=`\`headers().get(${h(arguments[0])})\``,t=b(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},has:{value:function(){let e=`\`headers().has(${h(arguments[0])})\``,t=b(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},set:{value:function(){let e=`\`headers().set(${h(arguments[0])}, ...)\``,t=b(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=b(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=b(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},keys:{value:function(){let e="`headers().keys()`",t=b(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},values:{value:function(){let e="`headers().values()`",t=b(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},entries:{value:function(){let e="`headers().entries()`",t=b(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=b(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}}}),a}else"prerender-ppr"===t.type?(0,o.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,o.throwToInterruptStaticGeneration)("headers",e,t);(0,o.trackDynamicDataInDynamicRender)(e,t)}return p((0,i.getExpectedRequestStore)("headers").headers)}let f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function h(e){return"string"==typeof e?`'${e}'`:"..."}let y=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(b);function b(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},6294:(e,t,r)=>{let n=r(3033),a=r(9294),i=r(4971),o=r(6926),s=r(23),c=r(8479);function l(){let e=a.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return u(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return u(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return f(null);default:return t}}function u(e,t){let r,n=d.get(l);return n||(r=f(e),d.set(e,r),r)}let d=new WeakMap;function f(e){let t=new p(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class p{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){y("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){y("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let h=(0,o.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function y(e){let t=a.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new c.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},6467:(e,t,r)=>{r.d(t,{y:()=>a});var n=r(6330);function a(e){return{createUser:({id:t,...r})=>e.user.create(i(r)),getUser:t=>e.user.findUnique({where:{id:t}}),getUserByEmail:t=>e.user.findUnique({where:{email:t}}),async getUserByAccount(t){let r=await e.account.findUnique({where:{provider_providerAccountId:t},include:{user:!0}});return r?.user??null},updateUser:({id:t,...r})=>e.user.update({where:{id:t},...i(r)}),deleteUser:t=>e.user.delete({where:{id:t}}),linkAccount:t=>e.account.create({data:t}),unlinkAccount:t=>e.account.delete({where:{provider_providerAccountId:t}}),async getSessionAndUser(t){let r=await e.session.findUnique({where:{sessionToken:t},include:{user:!0}});if(!r)return null;let{user:n,...a}=r;return{user:n,session:a}},createSession:t=>e.session.create(i(t)),updateSession:t=>e.session.update({where:{sessionToken:t.sessionToken},...i(t)}),deleteSession:t=>e.session.delete({where:{sessionToken:t}}),async createVerificationToken(t){let r=await e.verificationToken.create(i(t));return"id"in r&&r.id&&delete r.id,r},async useVerificationToken(t){try{let r=await e.verificationToken.delete({where:{identifier_token:t}});return"id"in r&&r.id&&delete r.id,r}catch(e){if(e instanceof n.Prisma.PrismaClientKnownRequestError&&"P2025"===e.code)return null;throw e}},getAccount:async(t,r)=>e.account.findFirst({where:{providerAccountId:t,provider:r}}),createAuthenticator:async t=>e.authenticator.create(i(t)),getAuthenticator:async t=>e.authenticator.findUnique({where:{credentialID:t}}),listAuthenticatorsByUserId:async t=>e.authenticator.findMany({where:{userId:t}}),updateAuthenticatorCounter:async(t,r)=>e.authenticator.update({where:{credentialID:t},data:{counter:r}})}}function i(e){let t={};for(let r in e)void 0!==e[r]&&(t[r]=e[r]);return{data:t}}},6897:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return u},getURLFromRedirectError:function(){return l},permanentRedirect:function(){return c},redirect:function(){return s}});let n=r(2836),a=r(9026),i=r(9121).actionAsyncStorage;function o(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(a.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=a.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",i}function s(e,t){var r;throw null!=t||(t=(null==i||null==(r=i.getStore())?void 0:r.isAction)?a.RedirectType.push:a.RedirectType.replace),o(e,t,n.RedirectStatusCode.TemporaryRedirect)}function c(e,t){throw void 0===t&&(t=a.RedirectType.replace),o(e,t,n.RedirectStatusCode.PermanentRedirect)}function l(e){return(0,a.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function u(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6926:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=i?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(1120));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}let i={current:null},o="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function c(e){return function(...t){s(e(...t))}}o(e=>{try{s(i.current)}finally{i.current=null}})},7576:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return u},RedirectType:function(){return a.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return c.unstable_rethrow}});let n=r(6897),a=r(9026),i=r(2765),o=r(8976),s=r(899),c=r(163);class l extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class u extends URLSearchParams{append(){throw new l}delete(){throw new l}set(){throw new l}sort(){throw new l}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8643:(e,t,r)=>{let n,a,i,o,s,c,l;r.d(t,{Ay:()=>op});var u={};r.r(u),r.d(u,{q:()=>tF,l:()=>tG});var d=function(e,t,r,n,a){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!a)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!a:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?a.call(e,r):a?a.value=r:t.set(e,r),r},f=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function p(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class h{constructor(e,t,r){if(rw.add(this),r_.set(this,{}),rv.set(this,void 0),rk.set(this,void 0),d(this,rk,r,"f"),d(this,rv,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(f(this,r_,"f")[e]=r)}get value(){return Object.keys(f(this,r_,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>f(this,r_,"f")[e]).join("")}chunk(e,t){let r=f(this,rw,"m",rA).call(this);for(let n of f(this,rw,"m",rE).call(this,{name:f(this,rv,"f").name,value:e,options:{...f(this,rv,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(f(this,rw,"m",rA).call(this))}}r_=new WeakMap,rv=new WeakMap,rk=new WeakMap,rw=new WeakSet,rE=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return f(this,r_,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,a=e.value.substr(3936*n,3936);r.push({...e,name:t,value:a}),f(this,r_,"f")[t]=a}return f(this,rk,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rA=function(){let e={};for(let t in f(this,r_,"f"))delete f(this,r_,"f")?.[t],e[t]={name:t,value:"",options:{...f(this,rv,"f").options,maxAge:0}};return e};class y extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class b extends y{}b.kind="signIn";class m extends y{}m.type="AdapterError";class g extends y{}g.type="AccessDenied";class x extends y{}x.type="CallbackRouteError";class w extends y{}w.type="ErrorPageLoop";class _ extends y{}_.type="EventError";class v extends y{}v.type="InvalidCallbackUrl";class k extends b{constructor(){super(...arguments),this.code="credentials"}}k.type="CredentialsSignin";class E extends y{}E.type="InvalidEndpoints";class A extends y{}A.type="InvalidCheck";class S extends y{}S.type="JWTSessionError";class R extends y{}R.type="MissingAdapter";class T extends y{}T.type="MissingAdapterMethods";class P extends y{}P.type="MissingAuthorize";class C extends y{}C.type="MissingSecret";class O extends b{}O.type="OAuthAccountNotLinked";class U extends b{}U.type="OAuthCallbackError";class $ extends y{}$.type="OAuthProfileParseError";class j extends y{}j.type="SessionTokenError";class I extends b{}I.type="OAuthSignInError";class H extends b{}H.type="EmailSignInError";class D extends y{}D.type="SignOutError";class W extends y{}W.type="UnknownAction";class K extends y{}K.type="UnsupportedStrategy";class L extends y{}L.type="InvalidProvider";class M extends y{}M.type="UntrustedHost";class N extends y{}N.type="Verification";class J extends b{}J.type="MissingCSRF";let B=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class q extends y{}q.type="DuplicateConditionalUI";class F extends y{}F.type="MissingWebAuthnAutocomplete";class z extends y{}z.type="WebAuthnVerificationError";class V extends b{}V.type="AccountNotLinked";class G extends y{}G.type="ExperimentalFeatureNotEnabled";let X=!1;function Y(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let Z=!1,Q=!1,ee=!1,et=["createVerificationToken","useVerificationToken","getUserByEmail"],er=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],en=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var ea=r(5511);let ei=(e,t,r,n,a)=>{let i=parseInt(e.substr(3),10)>>3||20,o=(0,ea.createHmac)(e,r.byteLength?r:new Uint8Array(i)).update(t).digest(),s=Math.ceil(a/i),c=new Uint8Array(i*s+n.byteLength+1),l=0,u=0;for(let t=1;t<=s;t++)c.set(n,u),c[u+n.byteLength]=t,c.set((0,ea.createHmac)(e,o).update(c.subarray(l,u+n.byteLength+1)).digest(),u),l=u,u+=i;return c.slice(0,a)};"function"!=typeof ea.hkdf||process.versions.electron||(n=async(...e)=>new Promise((t,r)=>{ea.hkdf(...e,(e,n)=>{e?r(e):t(new Uint8Array(n))})}));let eo=async(e,t,r,a,i)=>(n||ei)(e,t,r,a,i);function es(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function ec(e,t,r,n,a){return eo(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=es(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),es(r,"salt"),function(e){let t=es(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(a,e))}let el=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(r,t))},eu=new TextEncoder,ed=new TextDecoder;function ef(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function ep(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function eh(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return ep(r,t,0),ep(r,e%0x100000000,4),r}function ey(e){let t=new Uint8Array(4);return ep(t,e),t}function eb(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:ed.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=ed.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var r=t;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(r);let e=atob(r),n=new Uint8Array(e.length);for(let t=0;t<e.length;t++)n[t]=e.charCodeAt(t);return n}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function em(e){let t=e;return("string"==typeof t&&(t=eu.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class eg extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class ex extends eg{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class ew extends eg{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class e_ extends eg{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class ev extends eg{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class ek extends eg{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class eE extends eg{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class eA extends eg{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class eS extends eg{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class eR extends eg{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function eT(e){if(!eP(e))throw Error("CryptoKey instance expected")}function eP(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function eC(e){return e?.[Symbol.toStringTag]==="KeyObject"}let eO=e=>eP(e)||eC(e),eU=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function e$(e){return eU(e)&&"string"==typeof e.kty}function ej(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let eI=(e,...t)=>ej("Key must be ",e,...t);function eH(e,t,...r){return ej(`Key for the ${e} algorithm must be `,t,...r)}async function eD(e){if(eC(e))if("secret"!==e.type)return e.export({format:"jwk"});else e=e.export();if(e instanceof Uint8Array)return{kty:"oct",k:em(e)};if(!eP(e))throw TypeError(eI(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:a,...i}=await crypto.subtle.exportKey("jwk",e);return i}async function eW(e){return eD(e)}let eK=(e,t)=>{if("string"!=typeof e||!e)throw new eS(`${t} missing or invalid`)};async function eL(e,t){let r,n;if(e$(e))r=e;else if(eO(e))r=await eW(e);else throw TypeError(eI(e,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"EC":eK(r.crv,'"crv" (Curve) Parameter'),eK(r.x,'"x" (X Coordinate) Parameter'),eK(r.y,'"y" (Y Coordinate) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":eK(r.crv,'"crv" (Subtype of Key Pair) Parameter'),eK(r.x,'"x" (Public Key) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":eK(r.e,'"e" (Exponent) Parameter'),eK(r.n,'"n" (Modulus) Parameter'),n={e:r.e,kty:r.kty,n:r.n};break;case"oct":eK(r.k,'"k" (Key Value) Parameter'),n={k:r.k,kty:r.kty};break;default:throw new ev('"kty" (Key Type) Parameter missing or unsupported')}let a=eu.encode(JSON.stringify(n));return em(await el(t,a))}let eM=Symbol();function eN(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new ev(`Unsupported JWE Algorithm: ${e}`)}}let eJ=e=>crypto.getRandomValues(new Uint8Array(eN(e)>>3)),eB=(e,t)=>{if(t.length<<3!==eN(e))throw new eE("Invalid Initialization Vector length")},eq=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new eE(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function eF(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function ez(e,t){return e.name===t}function eV(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!ez(e.algorithm,"AES-GCM"))throw eF("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eF(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!ez(e.algorithm,"AES-KW"))throw eF("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eF(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw eF("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!ez(e.algorithm,"PBKDF2"))throw eF("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!ez(e.algorithm,"RSA-OAEP"))throw eF("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw eF(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var n=e,a=r;if(a&&!n.usages.includes(a))throw TypeError(`CryptoKey does not support this operation, its usages must include ${a}.`)}async function eG(e,t,r,n,a){if(!(r instanceof Uint8Array))throw TypeError(eI(r,"Uint8Array"));let i=parseInt(e.slice(1,4),10),o=await crypto.subtle.importKey("raw",r.subarray(i>>3),"AES-CBC",!1,["encrypt"]),s=await crypto.subtle.importKey("raw",r.subarray(0,i>>3),{hash:`SHA-${i<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await crypto.subtle.encrypt({iv:n,name:"AES-CBC"},o,t)),l=ef(a,n,c,eh(a.length<<3));return{ciphertext:c,tag:new Uint8Array((await crypto.subtle.sign("HMAC",s,l)).slice(0,i>>3)),iv:n}}async function eX(e,t,r,n,a){let i;r instanceof Uint8Array?i=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(eV(r,e,"encrypt"),i=r);let o=new Uint8Array(await crypto.subtle.encrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},i,t)),s=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:s,iv:n}}let eY=async(e,t,r,n,a)=>{if(!eP(r)&&!(r instanceof Uint8Array))throw TypeError(eI(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));switch(n?eB(e,n):n=eJ(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&eq(r,parseInt(e.slice(-3),10)),eG(e,t,r,n,a);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&eq(r,parseInt(e.slice(1,4),10)),eX(e,t,r,n,a);default:throw new ev("Unsupported JWE Content Encryption Algorithm")}};function eZ(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function eQ(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(eV(e,t,r),e)}async function e0(e,t,r){let n=await eQ(t,e,"wrapKey");eZ(n,e);let a=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",a,n,"AES-KW"))}async function e1(e,t,r){let n=await eQ(t,e,"unwrapKey");eZ(n,e);let a=await crypto.subtle.unwrapKey("raw",r,n,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",a))}function e2(e){return ef(ey(e.length),e)}async function e5(e,t,r){let n=Math.ceil((t>>3)/32),a=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(ey(t+1)),n.set(e,4),n.set(r,4+e.length),a.set(await el("sha256",n),32*t)}return a.slice(0,t>>3)}async function e6(e,t,r,n,a=new Uint8Array(0),i=new Uint8Array(0)){let o;eV(e,"ECDH"),eV(t,"ECDH","deriveBits");let s=ef(e2(eu.encode(r)),e2(a),e2(i),ey(n));return o="X25519"===e.algorithm.name?256:Math.ceil(parseInt(e.algorithm.namedCurve.slice(-3),10)/8)<<3,e5(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),n,s)}function e3(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}let e8=(e,t)=>ef(eu.encode(e),new Uint8Array([0]),t);async function e4(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new eE("PBES2 Salt Input must be 8 or more octets");let a=e8(t,e),i=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:a},s=await (n instanceof Uint8Array?crypto.subtle.importKey("raw",n,"PBKDF2",!1,["deriveBits"]):(eV(n,t,"deriveBits"),n));return new Uint8Array(await crypto.subtle.deriveBits(o,s,i))}async function e9(e,t,r,n=2048,a=crypto.getRandomValues(new Uint8Array(16))){let i=await e4(a,e,n,t);return{encryptedKey:await e0(e.slice(-6),i,r),p2c:n,p2s:em(a)}}async function e7(e,t,r,n,a){let i=await e4(a,e,n,t);return e1(e.slice(-6),i,r)}let te=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},tt=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new ev(`alg ${e} is not supported either by JOSE or your javascript runtime`)}};async function tr(e,t,r){return eV(t,e,"encrypt"),te(e,t),new Uint8Array(await crypto.subtle.encrypt(tt(e),t,r))}async function tn(e,t,r){return eV(t,e,"decrypt"),te(e,t),new Uint8Array(await crypto.subtle.decrypt(tt(e),t,r))}let ta=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new ev('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ev('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ev('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new ev('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n={...e};return delete n.alg,delete n.use,crypto.subtle.importKey("jwk",n,t,e.ext??!e.d,e.key_ops??r)},ti=async(e,t,r,n=!1)=>{let i=(a||=new WeakMap).get(e);if(i?.[r])return i[r];let o=await ta({...t,alg:r});return n&&Object.freeze(e),i?i[r]=o:a.set(e,{[r]:o}),o},to=(e,t)=>{let r,n=(a||=new WeakMap).get(e);if(n?.[t])return n[t];let i="public"===e.type,o=!!i;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,o,i?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,o,[i?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let n;switch(t){case"RSA-OAEP":n="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":n="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":n="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":n="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:n},o,i?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:n},o,[i?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let n=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),"ES384"===t&&"P-384"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),"ES512"===t&&"P-521"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:n},o,i?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:a.set(e,{[t]:r}),r},ts=async(e,t)=>{if(e instanceof Uint8Array||eP(e))return e;if(eC(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return to(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return ti(e,r,t)}if(e$(e))return e.k?eb(e.k):ti(e,e,t,!0);throw Error("unreachable")};function tc(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new ev(`Unsupported JWE Algorithm: ${e}`)}}let tl=e=>crypto.getRandomValues(new Uint8Array(tc(e)>>3));async function tu(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},n=await crypto.subtle.generateKey(r,!1,["sign"]),a=new Uint8Array(await crypto.subtle.sign(r,n,e)),i=new Uint8Array(await crypto.subtle.sign(r,n,t)),o=0,s=-1;for(;++s<32;)o|=a[s]^i[s];return 0===o}async function td(e,t,r,n,a,i){let o,s;if(!(t instanceof Uint8Array))throw TypeError(eI(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),l=await crypto.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),u=await crypto.subtle.importKey("raw",t.subarray(0,c>>3),{hash:`SHA-${c<<1}`,name:"HMAC"},!1,["sign"]),d=ef(i,n,r,eh(i.length<<3)),f=new Uint8Array((await crypto.subtle.sign("HMAC",u,d)).slice(0,c>>3));try{o=await tu(a,f)}catch{}if(!o)throw new ek;try{s=new Uint8Array(await crypto.subtle.decrypt({iv:n,name:"AES-CBC"},l,r))}catch{}if(!s)throw new ek;return s}async function tf(e,t,r,n,a,i){let o;t instanceof Uint8Array?o=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(eV(t,e,"decrypt"),o=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},o,ef(r,a)))}catch{throw new ek}}let tp=async(e,t,r,n,a,i)=>{if(!eP(t)&&!(t instanceof Uint8Array))throw TypeError(eI(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!n)throw new eE("JWE Initialization Vector missing");if(!a)throw new eE("JWE Authentication Tag missing");switch(eB(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&eq(t,parseInt(e.slice(-3),10)),td(e,t,r,n,a,i);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&eq(t,parseInt(e.slice(1,4),10)),tf(e,t,r,n,a,i);default:throw new ev("Unsupported JWE Content Encryption Algorithm")}};async function th(e,t,r,n){let a=e.slice(0,7),i=await eY(a,r,t,n,new Uint8Array(0));return{encryptedKey:i.ciphertext,iv:em(i.iv),tag:em(i.tag)}}async function ty(e,t,r,n,a){return tp(e.slice(0,7),t,r,n,a,new Uint8Array(0))}let tb=async(e,t,r,n,a={})=>{let i,o,s;switch(e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let c;if(eT(r),!e3(r))throw new ev("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:l,apv:u}=a;c=a.epk?await ts(a.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:d,y:f,crv:p,kty:h}=await eW(c),y=await e6(r,c,"ECDH-ES"===e?t:e,"ECDH-ES"===e?tc(t):parseInt(e.slice(-5,-2),10),l,u);if(o={epk:{x:d,crv:p,kty:h}},"EC"===h&&(o.epk.y=f),l&&(o.apu=em(l)),u&&(o.apv=em(u)),"ECDH-ES"===e){s=y;break}s=n||tl(t);let b=e.slice(-6);i=await e0(b,y,s);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||tl(t),eT(r),i=await tr(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||tl(t);let{p2c:c,p2s:l}=a;({encryptedKey:i,...o}=await e9(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=n||tl(t),i=await e0(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||tl(t);let{iv:c}=a;({encryptedKey:i,...o}=await th(e,r,s,c));break}default:throw new ev('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:i,parameters:o}},tm=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},tg=(e,t,r,n,a)=>{let i;if(void 0!==a.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(i=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!i.has(o))throw new ev(`Extension Header Parameter "${o}" is not recognized`);if(void 0===a[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(i.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)},tx=e=>e?.[Symbol.toStringTag],tw=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let n;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):n=r;break;case e.startsWith("PBES2"):n="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):n=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):n="wrapKey";break;case"decrypt"===r:n=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(n&&t.key_ops?.includes?.(n)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${n}" when present`)}return!0},t_=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(e$(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&tw(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!eO(t))throw TypeError(eH(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${tx(t)} instances for symmetric algorithms must be of type "secret"`)}},tv=(e,t,r)=>{if(e$(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&tw(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&tw(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!eO(t))throw TypeError(eH(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${tx(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${tx(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${tx(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${tx(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${tx(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},tk=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?t_(e,t,r):tv(e,t,r)};class tE{#e;#t;#r;#n;#a;#i;#o;#s;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#e=e}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setSharedUnprotectedHeader(e){if(this.#r)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#r=e,this}setUnprotectedHeader(e){if(this.#n)throw TypeError("setUnprotectedHeader can only be called once");return this.#n=e,this}setAdditionalAuthenticatedData(e){return this.#a=e,this}setContentEncryptionKey(e){if(this.#i)throw TypeError("setContentEncryptionKey can only be called once");return this.#i=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}async encrypt(e,t){let r,n,a,i,o;if(!this.#t&&!this.#n&&!this.#r)throw new eE("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!tm(this.#t,this.#n,this.#r))throw new eE("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this.#t,...this.#n,...this.#r};if(tg(eE,new Map,t?.crit,this.#t,s),void 0!==s.zip)throw new ev('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=s;if("string"!=typeof c||!c)throw new eE('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new eE('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#i&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);tk("dir"===c?l:c,e,"encrypt");{let a,i=await ts(e,c);({cek:n,encryptedKey:r,parameters:a}=await tb(c,l,i,this.#i,this.#s)),a&&(t&&eM in t?this.#n?this.#n={...this.#n,...a}:this.setUnprotectedHeader(a):this.#t?this.#t={...this.#t,...a}:this.setProtectedHeader(a))}i=this.#t?eu.encode(em(JSON.stringify(this.#t))):eu.encode(""),this.#a?(o=em(this.#a),a=ef(i,eu.encode("."),eu.encode(o))):a=i;let{ciphertext:u,tag:d,iv:f}=await eY(l,this.#e,n,this.#o,a),p={ciphertext:em(u)};return f&&(p.iv=em(f)),d&&(p.tag=em(d)),r&&(p.encrypted_key=em(r)),o&&(p.aad=o),this.#t&&(p.protected=ed.decode(i)),this.#r&&(p.unprotected=this.#r),this.#n&&(p.header=this.#n),p}}class tA{#c;constructor(e){this.#c=new tE(e)}setContentEncryptionKey(e){return this.#c.setContentEncryptionKey(e),this}setInitializationVector(e){return this.#c.setInitializationVector(e),this}setProtectedHeader(e){return this.#c.setProtectedHeader(e),this}setKeyManagementParameters(e){return this.#c.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this.#c.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tS=e=>Math.floor(e.getTime()/1e3),tR=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tT=e=>{let t,r=tR.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function tP(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let tC=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,tO=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class tU{#l;constructor(e){if(!eU(e))throw TypeError("JWT Claims Set MUST be an object");this.#l=structuredClone(e)}data(){return eu.encode(JSON.stringify(this.#l))}get iss(){return this.#l.iss}set iss(e){this.#l.iss=e}get sub(){return this.#l.sub}set sub(e){this.#l.sub=e}get aud(){return this.#l.aud}set aud(e){this.#l.aud=e}set jti(e){this.#l.jti=e}set nbf(e){"number"==typeof e?this.#l.nbf=tP("setNotBefore",e):e instanceof Date?this.#l.nbf=tP("setNotBefore",tS(e)):this.#l.nbf=tS(new Date)+tT(e)}set exp(e){"number"==typeof e?this.#l.exp=tP("setExpirationTime",e):e instanceof Date?this.#l.exp=tP("setExpirationTime",tS(e)):this.#l.exp=tS(new Date)+tT(e)}set iat(e){void 0===e?this.#l.iat=tS(new Date):e instanceof Date?this.#l.iat=tP("setIssuedAt",tS(e)):"string"==typeof e?this.#l.iat=tP("setIssuedAt",tS(new Date)+tT(e)):this.#l.iat=tP("setIssuedAt",e)}}class t${#i;#o;#s;#t;#u;#d;#f;#p;constructor(e={}){this.#p=new tU(e)}setIssuer(e){return this.#p.iss=e,this}setSubject(e){return this.#p.sub=e,this}setAudience(e){return this.#p.aud=e,this}setJti(e){return this.#p.jti=e,this}setNotBefore(e){return this.#p.nbf=e,this}setExpirationTime(e){return this.#p.exp=e,this}setIssuedAt(e){return this.#p.iat=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setContentEncryptionKey(e){if(this.#i)throw TypeError("setContentEncryptionKey can only be called once");return this.#i=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}replicateIssuerAsHeader(){return this.#u=!0,this}replicateSubjectAsHeader(){return this.#d=!0,this}replicateAudienceAsHeader(){return this.#f=!0,this}async encrypt(e,t){let r=new tA(this.#p.data());return this.#t&&(this.#u||this.#d||this.#f)&&(this.#t={...this.#t,iss:this.#u?this.#p.iss:void 0,sub:this.#d?this.#p.sub:void 0,aud:this.#f?this.#p.aud:void 0}),r.setProtectedHeader(this.#t),this.#o&&r.setInitializationVector(this.#o),this.#i&&r.setContentEncryptionKey(this.#i),this.#s&&r.setKeyManagementParameters(this.#s),r.encrypt(e,t)}}async function tj(e,t,r){let n;if(!eU(e))throw TypeError("JWK must be an object");switch(t??=e.alg,n??=r?.extractable??e.ext,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return eb(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new ev('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return ta({...e,alg:t,ext:n});default:throw new ev('Unsupported "kty" (Key Type) Parameter value')}}let tI=async(e,t,r,n,a)=>{switch(e){case"dir":if(void 0!==r)throw new eE("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new eE("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let a,i;if(!eU(n.epk))throw new eE('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(eT(t),!e3(t))throw new ev("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await tj(n.epk,e);if(eT(o),void 0!==n.apu){if("string"!=typeof n.apu)throw new eE('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{a=eb(n.apu)}catch{throw new eE("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new eE('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{i=eb(n.apv)}catch{throw new eE("Failed to base64url decode the apv")}}let s=await e6(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?tc(n.enc):parseInt(e.slice(-5,-2),10),a,i);if("ECDH-ES"===e)return s;if(void 0===r)throw new eE("JWE Encrypted Key missing");return e1(e.slice(-6),s,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new eE("JWE Encrypted Key missing");return eT(t),tn(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let i;if(void 0===r)throw new eE("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new eE('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=a?.maxPBES2Count||1e4;if(n.p2c>o)throw new eE('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new eE('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{i=eb(n.p2s)}catch{throw new eE("Failed to base64url decode the p2s")}return e7(e,t,r,n.p2c,i)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new eE("JWE Encrypted Key missing");return e1(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let a,i;if(void 0===r)throw new eE("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new eE('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new eE('JOSE Header "tag" (Authentication Tag) missing or invalid');try{a=eb(n.iv)}catch{throw new eE("Failed to base64url decode the iv")}try{i=eb(n.tag)}catch{throw new eE("Failed to base64url decode the tag")}return ty(e,t,r,a,i)}default:throw new ev('Invalid or unsupported "alg" (JWE Algorithm) header value')}},tH=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function tD(e,t,r){let n,a,i,o,s,c,l;if(!eU(e))throw new eE("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new eE("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new eE("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new eE("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new eE("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new eE("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new eE("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new eE("JWE AAD incorrect type");if(void 0!==e.header&&!eU(e.header))throw new eE("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eU(e.unprotected))throw new eE("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=eb(e.protected);n=JSON.parse(ed.decode(t))}catch{throw new eE("JWE Protected Header is invalid")}if(!tm(n,e.header,e.unprotected))throw new eE("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...n,...e.header,...e.unprotected};if(tg(eE,new Map,r?.crit,n,u),void 0!==u.zip)throw new ev('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:f}=u;if("string"!=typeof d||!d)throw new eE("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof f||!f)throw new eE("missing JWE Encryption Algorithm (enc) in JWE Header");let p=r&&tH("keyManagementAlgorithms",r.keyManagementAlgorithms),h=r&&tH("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(p&&!p.has(d)||!p&&d.startsWith("PBES2"))throw new e_('"alg" (Algorithm) Header Parameter value not allowed');if(h&&!h.has(f))throw new e_('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{a=eb(e.encrypted_key)}catch{throw new eE("Failed to base64url decode the encrypted_key")}let y=!1;"function"==typeof t&&(t=await t(n,e),y=!0),tk("dir"===d?f:d,t,"decrypt");let b=await ts(t,d);try{i=await tI(d,b,a,u,r)}catch(e){if(e instanceof TypeError||e instanceof eE||e instanceof ev)throw e;i=tl(f)}if(void 0!==e.iv)try{o=eb(e.iv)}catch{throw new eE("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=eb(e.tag)}catch{throw new eE("Failed to base64url decode the tag")}let m=eu.encode(e.protected??"");c=void 0!==e.aad?ef(m,eu.encode("."),eu.encode(e.aad)):m;try{l=eb(e.ciphertext)}catch{throw new eE("Failed to base64url decode the ciphertext")}let g={plaintext:await tp(f,i,l,o,s,c)};if(void 0!==e.protected&&(g.protectedHeader=n),void 0!==e.aad)try{g.additionalAuthenticatedData=eb(e.aad)}catch{throw new eE("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(g.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(g.unprotectedHeader=e.header),y)?{...g,key:b}:g}async function tW(e,t,r){if(e instanceof Uint8Array&&(e=ed.decode(e)),"string"!=typeof e)throw new eE("Compact JWE must be a string or Uint8Array");let{0:n,1:a,2:i,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new eE("Invalid Compact JWE");let l=await tD({ciphertext:o,iv:i||void 0,protected:n,tag:s||void 0,encrypted_key:a||void 0},t,r),u={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...u,key:l.key}:u}async function tK(e,t,r){let n=await tW(e,t,r),a=function(e,t,r={}){let n,a;try{n=JSON.parse(ed.decode(t))}catch{}if(!eU(n))throw new eA("JWT Claims Set must be a top-level JSON object");let{typ:i}=r;if(i&&("string"!=typeof e.typ||tC(e.typ)!==tC(i)))throw new ex('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:l,maxTokenAge:u}=r,d=[...o];for(let e of(void 0!==u&&d.push("iat"),void 0!==l&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new ex(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new ex('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new ex('unexpected "sub" claim value',n,"sub","check_failed");if(l&&!tO(n.aud,"string"==typeof l?[l]:l))throw new ex('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":a=tT(r.clockTolerance);break;case"number":a=r.clockTolerance;break;case"undefined":a=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:f}=r,p=tS(f||new Date);if((void 0!==n.iat||u)&&"number"!=typeof n.iat)throw new ex('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new ex('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>p+a)throw new ex('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new ex('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=p-a)throw new ew('"exp" claim timestamp check failed',n,"exp","check_failed")}if(u){let e=p-n.iat;if(e-a>("number"==typeof u?u:tT(u)))throw new ew('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-a)throw new ex('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n}(n.protectedHeader,n.plaintext,r),{protectedHeader:i}=n;if(void 0!==i.iss&&i.iss!==a.iss)throw new ex('replicated "iss" claim header parameter mismatch',a,"iss","mismatch");if(void 0!==i.sub&&i.sub!==a.sub)throw new ex('replicated "sub" claim header parameter mismatch',a,"sub","mismatch");if(void 0!==i.aud&&JSON.stringify(i.aud)!==JSON.stringify(a.aud))throw new ex('replicated "aud" claim header parameter mismatch',a,"aud","mismatch");let o={payload:a,protectedHeader:i};return"function"==typeof t?{...o,key:n.key}:o}let tL=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,tM=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,tN=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,tJ=/^[\u0020-\u003A\u003D-\u007E]*$/,tB=Object.prototype.toString,tq=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function tF(e,t){let r=new tq,n=e.length;if(n<2)return r;let a=t?.decode||tX,i=0;do{let t=e.indexOf("=",i);if(-1===t)break;let o=e.indexOf(";",i),s=-1===o?n:o;if(t>s){i=e.lastIndexOf(";",t-1)+1;continue}let c=tz(e,i,t),l=tV(e,t,c),u=e.slice(c,l);if(void 0===r[u]){let n=tz(e,t+1,s),i=tV(e,s,n),o=a(e.slice(n,i));r[u]=o}i=s+1}while(i<n);return r}function tz(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function tV(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function tG(e,t,r){let n=r?.encode||encodeURIComponent;if(!tL.test(e))throw TypeError(`argument name is invalid: ${e}`);let a=n(t);if(!tM.test(a))throw TypeError(`argument val is invalid: ${t}`);let i=e+"="+a;if(!r)return i;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError(`option maxAge is invalid: ${r.maxAge}`);i+="; Max-Age="+r.maxAge}if(r.domain){if(!tN.test(r.domain))throw TypeError(`option domain is invalid: ${r.domain}`);i+="; Domain="+r.domain}if(r.path){if(!tJ.test(r.path))throw TypeError(`option path is invalid: ${r.path}`);i+="; Path="+r.path}if(r.expires){var o;if(o=r.expires,"[object Date]"!==tB.call(o)||!Number.isFinite(r.expires.valueOf()))throw TypeError(`option expires is invalid: ${r.expires}`);i+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(i+="; HttpOnly"),r.secure&&(i+="; Secure"),r.partitioned&&(i+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":i+="; Priority=Low";break;case"medium":i+="; Priority=Medium";break;case"high":i+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${r.sameSite}`)}return i}function tX(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}let{q:tY}=u,tZ=()=>Date.now()/1e3|0,tQ="A256CBC-HS512";async function t0(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:a}=e,i=Array.isArray(r)?r:[r],o=await t2(tQ,i[0],a),s=await eL({kty:"oct",k:em(o)},`sha${o.byteLength<<3}`);return await new t$(t).setProtectedHeader({alg:"dir",enc:tQ,kid:s}).setIssuedAt().setExpirationTime(tZ()+n).setJti(crypto.randomUUID()).encrypt(o)}async function t1(e){let{token:t,secret:r,salt:n}=e,a=Array.isArray(r)?r:[r];if(!t)return null;let{payload:i}=await tK(t,async({kid:e,enc:t})=>{for(let r of a){let a=await t2(t,r,n);if(void 0===e||e===await eL({kty:"oct",k:em(a)},`sha${a.byteLength<<3}`))return a}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[tQ,"A256GCM"]});return i}async function t2(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await ec("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function t5({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:a}=e,i=n.origin;return t?i=await a.redirect({url:t,baseUrl:n.origin}):r&&(i=await a.redirect({url:r,baseUrl:n.origin})),{callbackUrl:i,callbackUrlCookie:i!==r?i:void 0}}let t6="\x1b[31m",t3="\x1b[0m",t8={error(e){let t=e instanceof y?e.type:e.name;if(console.error(`${t6}[auth][error]${t3} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${t6}[auth][cause]${t3}:`,t.stack),r&&console.error(`${t6}[auth][details]${t3}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn(`\x1b[33m[auth][warn][${e}]${t3}`,"Read more: https://warnings.authjs.dev")},debug(e,t){console.log(`\x1b[90m[auth][debug]:${t3} ${e}`,JSON.stringify(t,null,2))}};function t4(e){let t={...t8};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let t9=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:t7,l:re}=u;async function rt(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function rr(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new W("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:a}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new W(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new W(`Cannot parse action at ${e}`);let[a,i]=n;if(!t9.includes(a)||i&&!["signin","callback","webauthn-options"].includes(a))throw new W(`Cannot parse action at ${e}`);return{action:a,providerId:i}}(r.pathname,t.basePath);return{url:r,action:n,providerId:a,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await rt(e):void 0,cookies:t7(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=t4(t);r.error(n),r.debug("request",e)}}function rn(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:a}=e,i=re(r,n,a);t.has("Set-Cookie")?t.append("Set-Cookie",i):t.set("Set-Cookie",i)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function ra(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function ri(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function ro({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[a,i]=t.split("|");if(i===await ra(`${a}${e.secret}`))return{csrfTokenVerified:r&&a===n,csrfToken:a}}let a=ri(32),i=await ra(`${a}${e.secret}`);return{cookie:`${a}|${i}`,csrfToken:a}}function rs(e,t){if(!t)throw new J(`CSRF token was missing during an action ${e}`)}function rc(e){return null!==e&&"object"==typeof e}function rl(e,...t){if(!t.length)return e;let r=t.shift();if(rc(e)&&rc(r))for(let t in r)rc(r[t])?(rc(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),rl(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return rl(e,...t)}let ru=Symbol("skip-csrf-check"),rd=Symbol("return-type-raw"),rf=Symbol("custom-fetch"),rp=Symbol("conform-internal"),rh=e=>rb({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),ry=e=>rb({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function rb(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function rm(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let rg={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function rx({authOptions:e,providerId:t,action:r,url:n,cookies:a,callbackUrl:i,csrfToken:o,csrfDisabled:s,isPost:c}){var l,u;let d=t4(e),{providers:f,provider:h}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),a=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:a,...i}=t,o=a?.id??i.id,s=rl(i,a,{signinUrl:`${n}/signin/${o}`,callbackUrl:`${n}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=a?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=rm(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=rm(e.token,e.issuer),n=rm(e.userinfo,e.issuer),a=e.checks??["pkce"];return e.redirectProxyUrl&&(a.includes("state")||a.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:a,userinfo:n,profile:e.profile??rh,account:e.account??ry}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[rf]??(e[rf]=a?.[rf]),e}return s}),i=a.find(({id:e})=>e===t);if(t&&!i){let e=a.map(e=>e.id).join(", ");throw Error(`Provider with id "${t}" not found. Available providers: [${e}].`)}return{providers:a,provider:i}}({url:n,providerId:t,config:e}),y=!1;if((h?.type==="oauth"||h?.type==="oidc")&&h.redirectProxyUrl)try{y=new URL(h.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${h.redirectProxyUrl}`)}let b={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:h,cookies:rl(p(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:f,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:t0,decode:t1,...e.jwt},events:(l=e.events??{},u=d,Object.keys(l).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=l[t];return await r(...e)}catch(e){u.error(new _(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let a=e[n];return await a(...r)}catch(r){let e=new m(r);throw t.error(e),e}},r),{})}(e.adapter,d),callbacks:{...rg,...e.callbacks},logger:d,callbackUrl:n.origin,isOnRedirectProxy:y,experimental:{...e.experimental}},g=[];if(s)b.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await ro({options:b,cookieValue:a?.[b.cookies.csrfToken.name],isPost:c,bodyValue:o});b.csrfToken=e,b.csrfTokenVerified=r,t&&g.push({name:b.cookies.csrfToken.name,value:t,options:b.cookies.csrfToken.options})}let{callbackUrl:x,callbackUrlCookie:w}=await t5({options:b,cookieValue:a?.[b.cookies.callbackUrl.name],paramValue:i});return b.callbackUrl=x,w&&g.push({name:b.cookies.callbackUrl.name,value:w,options:b.cookies.callbackUrl.options}),{options:b,cookies:g}}var rw,r_,rv,rk,rE,rA,rS,rR,rT,rP,rC,rO,rU,r$,rj,rI,rH={},rD=[],rW=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,rK=Array.isArray;function rL(e,t){for(var r in t)e[r]=t[r];return e}function rM(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function rN(e,t,r){var n,a,i,o={};for(i in t)"key"==i?n=t[i]:"ref"==i?a=t[i]:o[i]=t[i];if(arguments.length>2&&(o.children=arguments.length>3?rS.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===o[i]&&(o[i]=e.defaultProps[i]);return rJ(e,o,n,a,null)}function rJ(e,t,r,n,a){var i={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==a?++rT:a,__i:-1,__u:0};return null==a&&null!=rR.vnode&&rR.vnode(i),i}function rB(e){return e.children}function rq(e,t){this.props=e,this.context=t}function rF(e,t){if(null==t)return e.__?rF(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rF(e):null}function rz(e){(!e.__d&&(e.__d=!0)&&rP.push(e)&&!rV.__r++||rC!==rR.debounceRendering)&&((rC=rR.debounceRendering)||rO)(rV)}function rV(){var e,t,r,n,a,i,o,s;for(rP.sort(rU);e=rP.shift();)e.__d&&(t=rP.length,n=void 0,i=(a=(r=e).__v).__e,o=[],s=[],r.__P&&((n=rL({},a)).__v=a.__v+1,rR.vnode&&rR.vnode(n),rQ(r.__P,n,a,r.__n,r.__P.namespaceURI,32&a.__u?[i]:null,o,null==i?rF(a):i,!!(32&a.__u),s),n.__v=a.__v,n.__.__k[n.__i]=n,r0(o,n,s),n.__e!=i&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)),rP.length>t&&rP.sort(rU));rV.__r=0}function rG(e,t,r,n,a,i,o,s,c,l,u){var d,f,p,h,y,b=n&&n.__k||rD,m=t.length;for(r.__d=c,function(e,t,r){var n,a,i,o,s,c=t.length,l=r.length,u=l,d=0;for(e.__k=[],n=0;n<c;n++)null!=(a=t[n])&&"boolean"!=typeof a&&"function"!=typeof a?(o=n+d,(a=e.__k[n]="string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?rJ(null,a,null,null,null):rK(a)?rJ(rB,{children:a},null,null,null):void 0===a.constructor&&a.__b>0?rJ(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=e,a.__b=e.__b+1,i=null,-1!==(s=a.__i=function(e,t,r,n){var a=e.key,i=e.type,o=r-1,s=r+1,c=t[r];if(null===c||c&&a==c.key&&i===c.type&&0==(131072&c.__u))return r;if(n>+(null!=c&&0==(131072&c.__u)))for(;o>=0||s<t.length;){if(o>=0){if((c=t[o])&&0==(131072&c.__u)&&a==c.key&&i===c.type)return o;o--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&a==c.key&&i===c.type)return s;s++}}return -1}(a,r,o,u))&&(u--,(i=r[s])&&(i.__u|=131072)),null==i||null===i.__v?(-1==s&&d--,"function"!=typeof a.type&&(a.__u|=65536)):s!==o&&(s==o-1?d--:s==o+1?d++:(s>o?d--:d++,a.__u|=65536))):a=e.__k[n]=null;if(u)for(n=0;n<l;n++)null!=(i=r[n])&&0==(131072&i.__u)&&(i.__e==e.__d&&(e.__d=rF(i)),function e(t,r,n){var a,i;if(rR.unmount&&rR.unmount(t),(a=t.ref)&&(a.current&&a.current!==t.__e||r1(a,null,r)),null!=(a=t.__c)){if(a.componentWillUnmount)try{a.componentWillUnmount()}catch(e){rR.__e(e,r)}a.base=a.__P=null}if(a=t.__k)for(i=0;i<a.length;i++)a[i]&&e(a[i],r,n||"function"!=typeof t.type);n||rM(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(i,i))}(r,t,b),c=r.__d,d=0;d<m;d++)null!=(p=r.__k[d])&&(f=-1===p.__i?rH:b[p.__i]||rH,p.__i=d,rQ(e,p,f,a,i,o,s,c,l,u),h=p.__e,p.ref&&f.ref!=p.ref&&(f.ref&&r1(f.ref,null,p),u.push(p.ref,p.__c||h,p)),null==y&&null!=h&&(y=h),65536&p.__u||f.__k===p.__k?c=function e(t,r,n){var a,i;if("function"==typeof t.type){for(a=t.__k,i=0;a&&i<a.length;i++)a[i]&&(a[i].__=t,r=e(a[i],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=rF(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(p,c,e):"function"==typeof p.type&&void 0!==p.__d?c=p.__d:h&&(c=h.nextSibling),p.__d=void 0,p.__u&=-196609);r.__d=c,r.__e=y}function rX(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||rW.test(t)?r:r+"px"}function rY(e,t,r,n,a){var i;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||rX(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||rX(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=r,r?n?r.u=n.u:(r.u=r$,e.addEventListener(t,i?rI:rj,i)):e.removeEventListener(t,i?rI:rj,i);else{if("http://www.w3.org/2000/svg"==a)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function rZ(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=r$++;else if(t.t<r.u)return;return r(rR.event?rR.event(t):t)}}}function rQ(e,t,r,n,a,i,o,s,c,l){var u,d,f,p,h,y,b,m,g,x,w,_,v,k,E,A,S=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(c=!!(32&r.__u),i=[s=t.__e=r.__e]),(u=rR.__b)&&u(t);e:if("function"==typeof S)try{if(m=t.props,g="prototype"in S&&S.prototype.render,x=(u=S.contextType)&&n[u.__c],w=u?x?x.props.value:u.__:n,r.__c?b=(d=t.__c=r.__c).__=d.__E:(g?t.__c=d=new S(m,w):(t.__c=d=new rq(m,w),d.constructor=S,d.render=r2),x&&x.sub(d),d.props=m,d.state||(d.state={}),d.context=w,d.__n=n,f=d.__d=!0,d.__h=[],d._sb=[]),g&&null==d.__s&&(d.__s=d.state),g&&null!=S.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=rL({},d.__s)),rL(d.__s,S.getDerivedStateFromProps(m,d.__s))),p=d.props,h=d.state,d.__v=t,f)g&&null==S.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),g&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(g&&null==S.getDerivedStateFromProps&&m!==p&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(m,w),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(m,d.__s,w)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=m,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),_=0;_<d._sb.length;_++)d.__h.push(d._sb[_]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(m,d.__s,w),g&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(p,h,y)})}if(d.context=w,d.props=m,d.__P=e,d.__e=!1,v=rR.__r,k=0,g){for(d.state=d.__s,d.__d=!1,v&&v(t),u=d.render(d.props,d.state,d.context),E=0;E<d._sb.length;E++)d.__h.push(d._sb[E]);d._sb=[]}else do d.__d=!1,v&&v(t),u=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++k<25);d.state=d.__s,null!=d.getChildContext&&(n=rL(rL({},n),d.getChildContext())),g&&!f&&null!=d.getSnapshotBeforeUpdate&&(y=d.getSnapshotBeforeUpdate(p,h)),rG(e,rK(A=null!=u&&u.type===rB&&null==u.key?u.props.children:u)?A:[A],t,r,n,a,i,o,s,c,l),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),b&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=i){for(t.__u|=c?160:128;s&&8===s.nodeType&&s.nextSibling;)s=s.nextSibling;i[i.indexOf(s)]=null,t.__e=s}else t.__e=r.__e,t.__k=r.__k;rR.__e(e,t,r)}else null==i&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,a,i,o,s,c){var l,u,d,f,p,h,y,b=r.props,m=t.props,g=t.type;if("svg"===g?a="http://www.w3.org/2000/svg":"math"===g?a="http://www.w3.org/1998/Math/MathML":a||(a="http://www.w3.org/1999/xhtml"),null!=i){for(l=0;l<i.length;l++)if((p=i[l])&&"setAttribute"in p==!!g&&(g?p.localName===g:3===p.nodeType)){e=p,i[l]=null;break}}if(null==e){if(null===g)return document.createTextNode(m);e=document.createElementNS(a,g,m.is&&m),s&&(rR.__m&&rR.__m(t,i),s=!1),i=null}if(null===g)b===m||s&&e.data===m||(e.data=m);else{if(i=i&&rS.call(e.childNodes),b=r.props||rH,!s&&null!=i)for(b={},l=0;l<e.attributes.length;l++)b[(p=e.attributes[l]).name]=p.value;for(l in b)if(p=b[l],"children"==l);else if("dangerouslySetInnerHTML"==l)d=p;else if(!(l in m)){if("value"==l&&"defaultValue"in m||"checked"==l&&"defaultChecked"in m)continue;rY(e,l,null,p,a)}for(l in m)p=m[l],"children"==l?f=p:"dangerouslySetInnerHTML"==l?u=p:"value"==l?h=p:"checked"==l?y=p:s&&"function"!=typeof p||b[l]===p||rY(e,l,p,b[l],a);if(u)s||d&&(u.__html===d.__html||u.__html===e.innerHTML)||(e.innerHTML=u.__html),t.__k=[];else if(d&&(e.innerHTML=""),rG(e,rK(f)?f:[f],t,r,n,"foreignObject"===g?"http://www.w3.org/1999/xhtml":a,i,o,i?i[0]:r.__k&&rF(r,0),s,c),null!=i)for(l=i.length;l--;)rM(i[l]);s||(l="value","progress"===g&&null==h?e.removeAttribute("value"):void 0===h||h===e[l]&&("progress"!==g||h)&&("option"!==g||h===b[l])||rY(e,l,h,b[l],a),l="checked",void 0!==y&&y!==e[l]&&rY(e,l,y,b[l],a))}return e}(r.__e,t,r,n,a,i,o,c,l);(u=rR.diffed)&&u(t)}function r0(e,t,r){t.__d=void 0;for(var n=0;n<r.length;n++)r1(r[n],r[++n],r[++n]);rR.__c&&rR.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rR.__e(e,t.__v)}})}function r1(e,t,r){try{if("function"==typeof e){var n="function"==typeof e.__u;n&&e.__u(),n&&null==t||(e.__u=e(t))}else e.current=t}catch(e){rR.__e(e,r)}}function r2(e,t,r){return this.constructor(e,r)}function r5(e,t){var r,n,a,i,o;r=e,rR.__&&rR.__(r,t),a=(n="function"==typeof r5)?null:r5&&r5.__k||t.__k,i=[],o=[],rQ(t,r=(!n&&r5||t).__k=rN(rB,null,[r]),a||rH,rH,t.namespaceURI,!n&&r5?[r5]:a?null:t.firstChild?rS.call(t.childNodes):null,i,!n&&r5?r5:a?a.__e:t.firstChild,n,o),r0(i,r,o)}rS=rD.slice,rR={__e:function(e,t,r,n){for(var a,i,o;t=t.__;)if((a=t.__c)&&!a.__)try{if((i=a.constructor)&&null!=i.getDerivedStateFromError&&(a.setState(i.getDerivedStateFromError(e)),o=a.__d),null!=a.componentDidCatch&&(a.componentDidCatch(e,n||{}),o=a.__d),o)return a.__E=a}catch(t){e=t}throw e}},rT=0,rq.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rL({},this.state),"function"==typeof e&&(e=e(rL({},r),this.props)),e&&rL(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rz(this))},rq.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rz(this))},rq.prototype.render=rB,rP=[],rO="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,rU=function(e,t){return e.__v.__b-t.__v.__b},rV.__r=0,r$=0,rj=rZ(!1),rI=rZ(!0);var r6=/[\s\n\\/='"\0<>]/,r3=/^(xlink|xmlns|xml)([A-Z])/,r8=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,r4=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,r9=new Set(["draggable","spellcheck"]),r7=/["&<]/;function ne(e){if(0===e.length||!1===r7.test(e))return e;for(var t=0,r=0,n="",a="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:a="&quot;";break;case 38:a="&amp;";break;case 60:a="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=a,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var nt={},nr=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),nn=/[A-Z]/g;function na(){this.__d=!0}var ni=null,no,ns,nc,nl,nu={},nd=[],nf=Array.isArray,np=Object.assign;function nh(e,t){var r,n=e.type,a=!0;return e.__c?(a=!1,(r=e.__c).state=r.__s):r=new n(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=nu),null==r.__s&&(r.__s=r.state),n.getDerivedStateFromProps?r.state=np({},r.state,n.getDerivedStateFromProps(r.props,r.state)):a&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!a&&r.componentWillUpdate&&r.componentWillUpdate(),nc&&nc(e),r.render(r.props,r.state,t)}var ny=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),nb=/["&<]/,nm=0;function ng(e,t,r,n,a,i){t||(t={});var o,s,c=t;"ref"in t&&(o=t.ref,delete t.ref);var l={type:e,props:c,key:r,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--nm,__i:-1,__u:0,__source:a,__self:i};if("function"==typeof e&&(o=e.defaultProps))for(s in o)void 0===c[s]&&(c[s]=o[s]);return rR.vnode&&rR.vnode(l),l}async function nx(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),i().forEach(e=>{n.searchParams.append(e.name,e.value)});let a=await fetch(n);return a.ok?a.json():void console.error("Failed to fetch options",a)}function a(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function i(){return Array.from(a().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=a();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await o("authenticate",n)}async function c(e){i().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=a();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let nw={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},n_=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function nv({html:e,title:t,status:r,cookies:n,theme:a,headTags:i}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${n_}</style><title>${t}</title>${i??""}</head><body class="__next-auth-theme-${a?.colorScheme??"auto"}"><div class="page">${function(e,t,r){var n=rR.__s;rR.__s=!0,no=rR.__b,ns=rR.diffed,nc=rR.__r,nl=rR.unmount;var a=rN(rB,null);a.__k=[e];try{var i=function e(t,r,n,a,i,o,s){if(null==t||!0===t||!1===t||""===t)return"";var c=typeof t;if("object"!=c)return"function"==c?"":"string"==c?ne(t):t+"";if(nf(t)){var l,u="";i.__k=t;for(var d=0;d<t.length;d++){var f=t[d];if(null!=f&&"boolean"!=typeof f){var p,h=e(f,r,n,a,i,o,s);"string"==typeof h?u+=h:(l||(l=[]),u&&l.push(u),u="",nf(h)?(p=l).push.apply(p,h):l.push(h))}}return l?(u&&l.push(u),l):u}if(void 0!==t.constructor)return"";t.__=i,no&&no(t);var y=t.type,b=t.props;if("function"==typeof y){var m,g,x,w=r;if(y===rB){if("tpl"in b){for(var _="",v=0;v<b.tpl.length;v++)if(_+=b.tpl[v],b.exprs&&v<b.exprs.length){var k=b.exprs[v];if(null==k)continue;"object"==typeof k&&(void 0===k.constructor||nf(k))?_+=e(k,r,n,a,t,o,s):_+=k}return _}if("UNSTABLE_comment"in b)return"\x3c!--"+ne(b.UNSTABLE_comment)+"--\x3e";g=b.children}else{if(null!=(m=y.contextType)){var E=r[m.__c];w=E?E.props.value:m.__}var A=y.prototype&&"function"==typeof y.prototype.render;if(A)g=nh(t,w),x=t.__c;else{t.__c=x={__v:t,context:w,props:t.props,setState:na,forceUpdate:na,__d:!0,__h:[]};for(var S=0;x.__d&&S++<25;)x.__d=!1,nc&&nc(t),g=y.call(x,b,w);x.__d=!0}if(null!=x.getChildContext&&(r=np({},r,x.getChildContext())),A&&rR.errorBoundaries&&(y.getDerivedStateFromError||x.componentDidCatch)){g=null!=g&&g.type===rB&&null==g.key&&null==g.props.tpl?g.props.children:g;try{return e(g,r,n,a,t,o,s)}catch(i){return y.getDerivedStateFromError&&(x.__s=y.getDerivedStateFromError(i)),x.componentDidCatch&&x.componentDidCatch(i,nu),x.__d?(g=nh(t,r),null!=(x=t.__c).getChildContext&&(r=np({},r,x.getChildContext())),e(g=null!=g&&g.type===rB&&null==g.key&&null==g.props.tpl?g.props.children:g,r,n,a,t,o,s)):""}finally{ns&&ns(t),t.__=null,nl&&nl(t)}}}g=null!=g&&g.type===rB&&null==g.key&&null==g.props.tpl?g.props.children:g;try{var R=e(g,r,n,a,t,o,s);return ns&&ns(t),t.__=null,rR.unmount&&rR.unmount(t),R}catch(i){if(!o&&s&&s.onError){var T=s.onError(i,t,function(i){return e(i,r,n,a,t,o,s)});if(void 0!==T)return T;var P=rR.__e;return P&&P(i,t),""}if(!o||!i||"function"!=typeof i.then)throw i;return i.then(function i(){try{return e(g,r,n,a,t,o,s)}catch(c){if(!c||"function"!=typeof c.then)throw c;return c.then(function(){return e(g,r,n,a,t,o,s)},i)}})}}var C,O="<"+y,U="";for(var $ in b){var j=b[$];if("function"!=typeof j||"class"===$||"className"===$){switch($){case"children":C=j;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in b)continue;$="for";break;case"className":if("class"in b)continue;$="class";break;case"defaultChecked":$="checked";break;case"defaultSelected":$="selected";break;case"defaultValue":case"value":switch($="value",y){case"textarea":C=j;continue;case"select":a=j;continue;case"option":a!=j||"selected"in b||(O+=" selected")}break;case"dangerouslySetInnerHTML":U=j&&j.__html;continue;case"style":"object"==typeof j&&(j=function(e){var t="";for(var r in e){var n=e[r];if(null!=n&&""!==n){var a="-"==r[0]?r:nt[r]||(nt[r]=r.replace(nn,"-$&").toLowerCase()),i=";";"number"!=typeof n||a.startsWith("--")||nr.has(a)||(i="px;"),t=t+a+":"+n+i}}return t||void 0}(j));break;case"acceptCharset":$="accept-charset";break;case"httpEquiv":$="http-equiv";break;default:if(r3.test($))$=$.replace(r3,"$1:$2").toLowerCase();else{if(r6.test($))continue;("-"===$[4]||r9.has($))&&null!=j?j+="":n?r4.test($)&&($="panose1"===$?"panose-1":$.replace(/([A-Z])/g,"-$1").toLowerCase()):r8.test($)&&($=$.toLowerCase())}}null!=j&&!1!==j&&(O=!0===j||""===j?O+" "+$:O+" "+$+'="'+("string"==typeof j?ne(j):j+"")+'"')}}if(r6.test(y))throw Error(y+" is not a valid HTML tag name in "+O+">");if(U||("string"==typeof C?U=ne(C):null!=C&&!1!==C&&!0!==C&&(U=e(C,r,"svg"===y||"foreignObject"!==y&&n,a,t,o,s))),ns&&ns(t),t.__=null,nl&&nl(t),!U&&ny.has(y))return O+"/>";var I="</"+y+">",H=O+">";return nf(U)?[H].concat(U,[I]):"string"!=typeof U?[H,U,I]:H+U+I}(e,nu,!1,void 0,a,!1,void 0);return nf(i)?i.join(""):i}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{rR.__c&&rR.__c(e,nd),rR.__s=n,nd.length=0}}(e)}</div></body></html>`}}function nk(e){let{url:t,theme:r,query:n,cookies:a,pages:i,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:a,callbackUrl:i})=>(e[t]={id:t,name:r,type:n,signinUrl:a,callbackUrl:i},e),{})}),signin(t,s){if(t)throw new W("Unsupported action");if(i?.signIn){let t=`${i.signIn}${i.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:a}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),l="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;l=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return nv({cookies:a,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:a,email:i,error:o}=e;"undefined"!=typeof document&&a?.brandColor&&document.documentElement.style.setProperty("--brand-color",a.brandColor),"undefined"!=typeof document&&a?.buttonText&&document.documentElement.style.setProperty("--button-text-color",a.buttonText);let s=o&&(nw[o]??nw.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return ng("div",{className:"signin",children:[a?.brandColor&&ng("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${a.brandColor}}`}}),a?.buttonText&&ng("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${a.buttonText}
        }
      `}}),ng("div",{className:"card",children:[s&&ng("div",{className:"error",children:ng("p",{children:s})}),a?.logo&&ng("img",{src:a.logo,alt:"Logo",className:"logo"}),r.map((e,a)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=s??o??"#fff";return ng("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?ng("form",{action:e.signinUrl,method:"POST",children:[ng("input",{type:"hidden",name:"csrfToken",value:t}),n&&ng("input",{type:"hidden",name:"callbackUrl",value:n}),ng("button",{type:"submit",className:"button",style:{"--provider-brand-color":l},tabIndex:0,children:[ng("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&ng("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&a>0&&"email"!==r[a-1].type&&"credentials"!==r[a-1].type&&"webauthn"!==r[a-1].type&&ng("hr",{}),"email"===e.type&&ng("form",{action:e.signinUrl,method:"POST",children:[ng("input",{type:"hidden",name:"csrfToken",value:t}),ng("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),ng("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:i,placeholder:"<EMAIL>",required:!0}),ng("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&ng("form",{action:e.callbackUrl,method:"POST",children:[ng("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>ng("div",{children:[ng("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),ng("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),ng("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&ng("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[ng("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>ng("div",{children:[ng("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),ng("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),ng("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&a+1<r.length&&ng("hr",{})]},e.id)})]}),c&&ng(rB,{children:ng("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${nx})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:l})},signout:()=>i?.signOut?{redirect:i.signOut,cookies:a}:nv({cookies:a,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return ng("div",{className:"signout",children:[n?.brandColor&&ng("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&ng("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),ng("div",{className:"card",children:[n?.logo&&ng("img",{src:n.logo,alt:"Logo",className:"logo"}),ng("h1",{children:"Signout"}),ng("p",{children:"Are you sure you want to sign out?"}),ng("form",{action:t?.toString(),method:"POST",children:[ng("input",{type:"hidden",name:"csrfToken",value:r}),ng("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>i?.verifyRequest?{redirect:`${i.verifyRequest}${t?.search??""}`,cookies:a}:nv({cookies:a,theme:r,html:function(e){let{url:t,theme:r}=e;return ng("div",{className:"verify-request",children:[r.brandColor&&ng("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),ng("div",{className:"card",children:[r.logo&&ng("img",{src:r.logo,alt:"Logo",className:"logo"}),ng("h1",{children:"Check your email"}),ng("p",{children:"A sign in link has been sent to your email address."}),ng("p",{children:ng("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>i?.error?{redirect:`${i.error}${i.error.includes("?")?"&":"?"}error=${e}`,cookies:a}:nv({cookies:a,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,a=`${t}/signin`,i={default:{status:200,heading:"Error",message:ng("p",{children:ng("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:ng("div",{children:[ng("p",{children:"There is a problem with the server configuration."}),ng("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:ng("div",{children:[ng("p",{children:"You do not have permission to sign in."}),ng("p",{children:ng("a",{className:"button",href:a,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:ng("div",{children:[ng("p",{children:"The sign in link is no longer valid."}),ng("p",{children:"It may have been used already or it may have expired."})]}),signin:ng("a",{className:"button",href:a,children:"Sign in"})}},{status:o,heading:s,message:c,signin:l}=i[r]??i.default;return{status:o,html:ng("div",{className:"error",children:[n?.brandColor&&ng("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),ng("div",{className:"card",children:[n?.logo&&ng("img",{src:n?.logo,alt:"Logo",className:"logo"}),ng("h1",{children:s}),ng("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function nE(e,t=Date.now()){return new Date(t+1e3*e)}async function nA(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:a,jwt:i,events:o,session:{strategy:s,generateSessionToken:c}}=n;if(!a)return{user:t,account:r};let l=r,{createUser:u,updateUser:d,getUser:f,getUserByAccount:p,getUserByEmail:h,linkAccount:y,createSession:b,getSessionAndUser:m,deleteSession:g}=a,x=null,w=null,_=!1,v="jwt"===s;if(e)if(v)try{let t=n.cookies.sessionToken.name;(x=await i.decode({...i,token:e,salt:t}))&&"sub"in x&&x.sub&&(w=await f(x.sub))}catch{}else{let t=await m(e);t&&(x=t.session,w=t.user)}if("email"===l.type){let r=await h(t.email);return r?(w?.id!==r.id&&!v&&e&&await g(e),w=await d({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:w})):(w=await u({...t,emailVerified:new Date}),await o.createUser?.({user:w}),_=!0),{session:x=v?{}:await b({sessionToken:c(),userId:w.id,expires:nE(n.session.maxAge)}),user:w,isNewUser:_}}if("webauthn"===l.type){let e=await p({providerAccountId:l.providerAccountId,provider:l.provider});if(e){if(w){if(e.id===w.id){let e={...l,userId:w.id};return{session:x,user:w,isNewUser:_,account:e}}throw new V("The account is already associated with another user",{provider:l.provider})}x=v?{}:await b({sessionToken:c(),userId:e.id,expires:nE(n.session.maxAge)});let t={...l,userId:e.id};return{session:x,user:e,isNewUser:_,account:t}}{if(w){await y({...l,userId:w.id}),await o.linkAccount?.({user:w,account:l,profile:t});let e={...l,userId:w.id};return{session:x,user:w,isNewUser:_,account:e}}if(t.email?await h(t.email):null)throw new V("Another account already exists with the same e-mail address",{provider:l.provider});w=await u({...t}),await o.createUser?.({user:w}),await y({...l,userId:w.id}),await o.linkAccount?.({user:w,account:l,profile:t}),x=v?{}:await b({sessionToken:c(),userId:w.id,expires:nE(n.session.maxAge)});let e={...l,userId:w.id};return{session:x,user:w,isNewUser:!0,account:e}}}let k=await p({providerAccountId:l.providerAccountId,provider:l.provider});if(k){if(w){if(k.id===w.id)return{session:x,user:w,isNewUser:_};throw new O("The account is already associated with another user",{provider:l.provider})}return{session:x=v?{}:await b({sessionToken:c(),userId:k.id,expires:nE(n.session.maxAge)}),user:k,isNewUser:_}}{let{provider:e}=n,{type:r,provider:a,providerAccountId:i,userId:s,...d}=l;if(l=Object.assign(e.account(d)??{},{providerAccountId:i,provider:a,type:r,userId:s}),w)return await y({...l,userId:w.id}),await o.linkAccount?.({user:w,account:l,profile:t}),{session:x,user:w,isNewUser:_};let f=t.email?await h(t.email):null;if(f){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)w=f,_=!1;else throw new O("Another account already exists with the same e-mail address",{provider:l.provider})}else w=await u({...t,emailVerified:null}),_=!0;return await o.createUser?.({user:w}),await y({...l,userId:w.id}),await o.linkAccount?.({user:w,account:l,profile:t}),{session:x=v?{}:await b({sessionToken:c(),userId:w.id,expires:nE(n.session.maxAge)}),user:w,isNewUser:_}}}function nS(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(i="oauth4webapi/v3.5.3");let nR="ERR_INVALID_ARG_VALUE",nT="ERR_INVALID_ARG_TYPE";function nP(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let nC=Symbol(),nO=Symbol(),nU=Symbol(),n$=Symbol(),nj=Symbol(),nI=Symbol(),nH=Symbol(),nD=new TextEncoder,nW=new TextDecoder;function nK(e){return"string"==typeof e?nD.encode(e):nW.decode(e)}function nL(e){return"string"==typeof e?s(e):o(e)}o=Uint8Array.prototype.toBase64?e=>(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e.toBase64({alphabet:"base64url",omitPadding:!0})):e=>{e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},s=Uint8Array.fromBase64?e=>{try{return Uint8Array.fromBase64(e,{alphabet:"base64url"})}catch(e){throw nP("The input to be decoded is not correctly encoded.",nR,e)}}:e=>{try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw nP("The input to be decoded is not correctly encoded.",nR,e)}};class nM extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aY,Error.captureStackTrace?.(this,this.constructor)}}class nN extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function nJ(e,t,r){return new nN(e,{code:t,cause:r})}function nB(e,t){if(!(e instanceof CryptoKey))throw nP(`${t} must be a CryptoKey`,nT)}function nq(e,t){if(nB(e,t),"private"!==e.type)throw nP(`${t} must be a private CryptoKey`,nR)}function nF(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function nz(e){nS(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e??{});if(i&&!t.has("user-agent")&&t.set("user-agent",i),t.has("authorization"))throw nP('"options.headers" must not include the "authorization" header name',nR);return t}function nV(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw nP('"options.signal" must return or be an instance of AbortSignal',nT);return e}function nG(e){return e.includes("//")?e.replace("//","/"):e}async function nX(e,t,r,n){if(!(e instanceof URL))throw nP(`"${t}" must be an instance of URL`,nT);ao(e,n?.[nC]!==!0);let a=r(new URL(e.href)),i=nz(n?.headers);return i.set("accept","application/json"),(n?.[n$]||fetch)(a.href,{body:void 0,headers:Object.fromEntries(i.entries()),method:"GET",redirect:"manual",signal:n?.signal?nV(n.signal):void 0})}async function nY(e,t){return nX(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=nG(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r,n;n=".well-known/oauth-authorization-server","/"===(r=e).pathname?r.pathname=n:r.pathname=nG(`${n}/${r.pathname}`);break;default:throw nP('"options.algorithm" must be "oidc" (default), or "oauth2"',nR)}return e},t)}function nZ(e,t,r,n,a){try{if("number"!=typeof e||!Number.isFinite(e))throw nP(`${r} must be a number`,nT,a);if(e>0)return;if(t){if(0!==e)throw nP(`${r} must be a non-negative number`,nR,a);return}throw nP(`${r} must be a positive number`,nR,a)}catch(e){if(n)throw nJ(e.message,n,a);throw e}}function nQ(e,t,r,n){try{if("string"!=typeof e)throw nP(`${t} must be a string`,nT,n);if(0===e.length)throw nP(`${t} must not be empty`,nR,n)}catch(e){if(r)throw nJ(e.message,r,n);throw e}}async function n0(e,t){if(!(e instanceof URL)&&e!==iS)throw nP('"expectedIssuerIdentifier" must be an instance of URL',nT);if(!nS(t,Response))throw nP('"response" must be an instance of Response',nT);if(200!==t.status)throw nJ('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',a6,t);ii(t);let r=await iA(t);if(nQ(r.issuer,'"response" body "issuer" property',a1,{body:r}),e!==iS&&new URL(r.issuer).href!==e.href)throw nJ('"response" body "issuer" property does not match the expected value',a7,{expected:e.href,body:r,attribute:"issuer"});return r}function n1(e){var t=e,r="application/json";if(aS(t)!==r)throw n2(t,r)}function n2(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return nJ(r,a5,e)}function n5(){return nL(crypto.getRandomValues(new Uint8Array(32)))}async function n6(e){return nQ(e,"codeVerifier"),nL(await crypto.subtle.digest("SHA-256",nK(e)))}function n3(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new nM("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new nM("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new nM("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nM("unsupported CryptoKey algorithm name",{cause:e})}}function n8(e){let t=e?.[nO];return"number"==typeof t&&Number.isFinite(t)?t:0}function n4(e){let t=e?.[nU];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function n9(){return Math.floor(Date.now()/1e3)}function n7(e){if("object"!=typeof e||null===e)throw nP('"as" must be an object',nT);nQ(e.issuer,'"as.issuer"')}function ae(e){if("object"!=typeof e||null===e)throw nP('"client" must be an object',nT);nQ(e.client_id,'"client.client_id"')}function at(e,t){let r=n9()+n8(t);return{jti:n5(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function ar(e,t,r){if(!r.usages.includes("sign"))throw nP('CryptoKey instances used for signing assertions must include "sign" in their "usages"',nR);let n=`${nL(nK(JSON.stringify(e)))}.${nL(nK(JSON.stringify(t)))}`,a=nL(await crypto.subtle.sign(iu(r),r,nK(n)));return`${n}.${a}`}async function an(e){let{kty:t,e:r,n,x:a,y:i,crv:o}=await crypto.subtle.exportKey("jwk",e),s={kty:t,e:r,n,x:a,y:i,crv:o};return c.set(e,s),s}async function aa(e){return(c||=new WeakMap).get(e)||an(e)}let ai=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function ao(e,t){if(t&&"https:"!==e.protocol)throw nJ("only requests to HTTPS are allowed",a3,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw nJ("only HTTP and HTTPS requests are allowed",a8,e)}function as(e,t,r,n){let a;if("string"!=typeof e||!(a=ai(e)))throw nJ(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?it:ir,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return ao(a,n),a}function ac(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?as(e.mtls_endpoint_aliases[t],t,r,n):as(e[t],t,r,n)}class al extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aX,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class au extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aZ,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class ad extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aG,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let af="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",ap=RegExp("^[,\\s]*("+af+")\\s(.*)"),ah=RegExp("^[,\\s]*("+af+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),ay=RegExp("^[,\\s]*"+("("+af+")\\s*=\\s*(")+af+")[,\\s]*(.*)"),ab=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function am(e){if(e.status>399&&e.status<500){ii(e),n1(e);try{let t=await e.clone().json();if(nF(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function ag(e,t,r){if(e.status!==t){let t;if(t=await am(e))throw await e.body?.cancel(),new al("server responded with an error in the response body",{cause:t,response:e});throw nJ(`"response" is not a conform ${r} response (unexpected HTTP status code)`,a6,e)}}function ax(e){if(!aK.has(e))throw nP('"options.DPoP" is not a valid DPoPHandle',nR)}async function aw(e,t,r,n,a,i){if(nQ(e,'"accessToken"'),!(r instanceof URL))throw nP('"url" must be an instance of URL',nT);ao(r,i?.[nC]!==!0),n=nz(n),i?.DPoP&&(ax(i.DPoP),await i.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (i?.[n$]||fetch)(r.href,{body:a,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:i?.signal?nV(i.signal):void 0});return i?.DPoP?.cacheNonce(o),o}async function a_(e,t,r,n){n7(e),ae(t);let a=ac(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[nC]!==!0),i=nz(n?.headers);return t.userinfo_signed_response_alg?i.set("accept","application/jwt"):(i.set("accept","application/json"),i.append("accept","application/jwt")),aw(r,"GET",a,i,null,{...n,[nO]:n8(t)})}function av(e,t,r,n){(l||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return n9()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function ak(e,t){l?.delete(e),delete t?.jwks,delete t?.uat}async function aE(e,t,r){var n;let a,i,o,{alg:s,kid:c}=r;if(function(e){if(!ic(e.alg))throw new nM('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!l?.has(e)&&!("object"!=typeof(n=t?.[nH])||null===n||!("uat"in n)||"number"!=typeof n.uat||n9()-n.uat>=300)&&"jwks"in n&&nF(n.jwks)&&Array.isArray(n.jwks.keys)&&Array.prototype.every.call(n.jwks.keys,nF)&&av(e,t?.[nH].jwks,t?.[nH].uat),l?.has(e)){if({jwks:a,age:i}=l.get(e),i>=300)return ak(e,t?.[nH]),aE(e,t,r)}else a=await io(e,t).then(is),i=0,av(e,a,n9(),t?.[nH]);switch(s.slice(0,2)){case"RS":case"PS":o="RSA";break;case"ES":o="EC";break;case"Ed":o="OKP";break;default:throw new nM("unsupported JWS algorithm",{cause:{alg:s}})}let u=a.keys.filter(e=>{if(e.kty!==o||void 0!==c&&c!==e.kid||void 0!==e.alg&&s!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===s&&"P-256"!==e.crv:case"ES384"===s&&"P-384"!==e.crv:case"ES512"===s&&"P-521"!==e.crv:case"Ed25519"===s&&"Ed25519"!==e.crv:case"EdDSA"===s&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:f}=u;if(!f){if(i>=60)return ak(e,t?.[nH]),aE(e,t,r);throw nJ("error when selecting a JWT verification key, no applicable keys found",ie,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)})}if(1!==f)throw nJ('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',ie,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)});return ik(s,d)}let aA=Symbol();function aS(e){return e.headers.get("content-type")?.split(";")[0]}async function aR(e,t,r,n,a){let i;if(n7(e),ae(t),!nS(n,Response))throw nP('"response" must be an instance of Response',nT);if(aj(n),200!==n.status)throw nJ('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',a6,n);if(ii(n),"application/jwt"===aS(n)){let{claims:r,jwt:o}=await ip(await n.text(),ig.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),n8(t),n4(t),a?.[nI]).then(aI.bind(void 0,t.client_id)).then(aD.bind(void 0,e));aO.set(n,o),i=r}else{if(t.userinfo_signed_response_alg)throw nJ("JWT UserInfo Response expected",aQ,n);i=await iA(n)}if(nQ(i.sub,'"response" body "sub" property',a1,{body:i}),r===aA);else if(nQ(r,'"expectedSubject"'),i.sub!==r)throw nJ('unexpected "response" body "sub" property value',a7,{expected:r,body:i,attribute:"sub"});return i}async function aT(e,t,r,n,a,i,o){return await r(e,t,a,i),i.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[n$]||fetch)(n.href,{body:a,headers:Object.fromEntries(i.entries()),method:"POST",redirect:"manual",signal:o?.signal?nV(o.signal):void 0})}async function aP(e,t,r,n,a,i){let o=ac(e,"token_endpoint",t.use_mtls_endpoint_aliases,i?.[nC]!==!0);a.set("grant_type",n);let s=nz(i?.headers);s.set("accept","application/json"),i?.DPoP!==void 0&&(ax(i.DPoP),await i.DPoP.addProof(o,s,"POST"));let c=await aT(e,t,r,o,a,s,i);return i?.DPoP?.cacheNonce(c),c}let aC=new WeakMap,aO=new WeakMap;function aU(e){if(!e.id_token)return;let t=aC.get(e);if(!t)throw nP('"ref" was already garbage collected or did not resolve from the proper sources',nR);return t}async function a$(e,t,r,n,a){if(n7(e),ae(t),!nS(r,Response))throw nP('"response" must be an instance of Response',nT);aj(r),await ag(r,200,"Token Endpoint"),ii(r);let i=await iA(r);if(nQ(i.access_token,'"response" body "access_token" property',a1,{body:i}),nQ(i.token_type,'"response" body "token_type" property',a1,{body:i}),i.token_type=i.token_type.toLowerCase(),"dpop"!==i.token_type&&"bearer"!==i.token_type)throw new nM("unsupported `token_type` value",{cause:{body:i}});if(void 0!==i.expires_in){let e="number"!=typeof i.expires_in?parseFloat(i.expires_in):i.expires_in;nZ(e,!1,'"response" body "expires_in" property',a1,{body:i}),i.expires_in=e}if(void 0!==i.refresh_token&&nQ(i.refresh_token,'"response" body "refresh_token" property',a1,{body:i}),void 0!==i.scope&&"string"!=typeof i.scope)throw nJ('"response" body "scope" property must be a string',a1,{body:i});if(void 0!==i.id_token){nQ(i.id_token,'"response" body "id_token" property',a1,{body:i});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(nZ(t.default_max_age,!1,'"client.default_max_age"'),o.push("auth_time")),n?.length&&o.push(...n);let{claims:s,jwt:c}=await ip(i.id_token,ig.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),n8(t),n4(t),a?.[nI]).then(aJ.bind(void 0,o)).then(aW.bind(void 0,e)).then(aH.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw nJ('ID Token "aud" (audience) claim includes additional untrusted audiences',a9,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw nJ('unexpected ID Token "azp" (authorized party) claim value',a9,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&nZ(s.auth_time,!1,'ID Token "auth_time" (authentication time)',a1,{claims:s}),aO.set(r,c),aC.set(i,s)}return i}function aj(e){let t;if(t=function(e){if(!nS(e,Response))throw nP('"response" must be an instance of Response',nT);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(ap),a=t?.["1"].toLowerCase();if(n=t?.["2"],!a)return;let i={};for(;n;){let r,a;if(t=n.match(ah)){if([,r,a,n]=t,a.includes("\\"))try{a=JSON.parse(`"${a}"`)}catch{}i[r.toLowerCase()]=a;continue}if(t=n.match(ay)){[,r,a,n]=t,i[r.toLowerCase()]=a;continue}if(t=n.match(ab)){if(Object.keys(i).length)break;[,e,n]=t;break}return}let o={scheme:a,parameters:i};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new ad("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function aI(e,t){return void 0!==t.claims.aud?aH(e,t):t}function aH(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw nJ('unexpected JWT "aud" (audience) claim value',a9,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw nJ('unexpected JWT "aud" (audience) claim value',a9,{expected:e,claims:t.claims,claim:"aud"});return t}function aD(e,t){return void 0!==t.claims.iss?aW(e,t):t}function aW(e,t){let r=e[iR]?.(t)??e.issuer;if(t.claims.iss!==r)throw nJ('unexpected JWT "iss" (issuer) claim value',a9,{expected:r,claims:t.claims,claim:"iss"});return t}let aK=new WeakSet,aL=Symbol();async function aM(e,t,r,n,a,i,o){if(n7(e),ae(t),!aK.has(n))throw nP('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',nR);nQ(a,'"redirectUri"');let s=ix(n,"code");if(!s)throw nJ('no authorization code in "callbackParameters"',a1);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",a),c.set("code",s),i!==aL&&(nQ(i,'"codeVerifier"'),c.set("code_verifier",i)),aP(e,t,r,"authorization_code",c,o)}let aN={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function aJ(e,t){for(let r of e)if(void 0===t.claims[r])throw nJ(`JWT "${r}" (${aN[r]}) claim missing`,a1,{claims:t.claims});return t}let aB=Symbol(),aq=Symbol();async function aF(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?az(e,t,r,n.expectedNonce,n.maxAge,{[nI]:n[nI]}):aV(e,t,r,n)}async function az(e,t,r,n,a,i){let o=[];switch(n){case void 0:n=aB;break;case aB:break;default:nQ(n,'"expectedNonce" argument'),o.push("nonce")}switch(a??=t.default_max_age){case void 0:a=aq;break;case aq:break;default:nZ(a,!1,'"maxAge" argument'),o.push("auth_time")}let s=await a$(e,t,r,o,i);nQ(s.id_token,'"response" body "id_token" property',a1,{body:s});let c=aU(s);if(a!==aq){let e=n9()+n8(t),r=n4(t);if(c.auth_time+a<e-r)throw nJ("too much time has elapsed since the last End-User authentication",a4,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===aB){if(void 0!==c.nonce)throw nJ('unexpected ID Token "nonce" claim value',a9,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw nJ('unexpected ID Token "nonce" claim value',a9,{expected:n,claims:c,claim:"nonce"});return s}async function aV(e,t,r,n){let a=await a$(e,t,r,void 0,n),i=aU(a);if(i){if(void 0!==t.default_max_age){nZ(t.default_max_age,!1,'"client.default_max_age"');let e=n9()+n8(t),r=n4(t);if(i.auth_time+t.default_max_age<e-r)throw nJ("too much time has elapsed since the last End-User authentication",a4,{claims:i,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==i.nonce)throw nJ('unexpected ID Token "nonce" claim value',a9,{expected:void 0,claims:i,claim:"nonce"})}return a}let aG="OAUTH_WWW_AUTHENTICATE_CHALLENGE",aX="OAUTH_RESPONSE_BODY_ERROR",aY="OAUTH_UNSUPPORTED_OPERATION",aZ="OAUTH_AUTHORIZATION_RESPONSE_ERROR",aQ="OAUTH_JWT_USERINFO_EXPECTED",a0="OAUTH_PARSE_ERROR",a1="OAUTH_INVALID_RESPONSE",a2="OAUTH_INVALID_REQUEST",a5="OAUTH_RESPONSE_IS_NOT_JSON",a6="OAUTH_RESPONSE_IS_NOT_CONFORM",a3="OAUTH_HTTP_REQUEST_FORBIDDEN",a8="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",a4="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",a9="OAUTH_JWT_CLAIM_COMPARISON_FAILED",a7="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",ie="OAUTH_KEY_SELECTION_FAILED",it="OAUTH_MISSING_SERVER_METADATA",ir="OAUTH_INVALID_SERVER_METADATA";function ia(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw nJ('unexpected JWT "typ" header parameter value',a1,{header:t.header});return t}function ii(e){if(e.bodyUsed)throw nP('"response" body has been used already',nR)}async function io(e,t){n7(e);let r=ac(e,"jwks_uri",!1,t?.[nC]!==!0),n=nz(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[n$]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?nV(t.signal):void 0})}async function is(e){if(!nS(e,Response))throw nP('"response" must be an instance of Response',nT);if(200!==e.status)throw nJ('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',a6,e);ii(e);let t=await iA(e,e=>(function(e,...t){if(!t.includes(aS(e)))throw n2(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw nJ('"response" body "keys" property must be an array',a1,{body:t});if(!Array.prototype.every.call(t.keys,nF))throw nJ('"response" body "keys" property members must be JWK formatted objects',a1,{body:t});return t}function ic(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function il(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new nM(`unsupported ${t.name} modulusLength`,{cause:e})}function iu(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new nM("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(il(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new nM("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return il(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new nM("unsupported CryptoKey algorithm name",{cause:e})}async function id(e,t,r,n){let a=nK(`${e}.${t}`),i=iu(r);if(!await crypto.subtle.verify(i,r,n,a))throw nJ("JWT signature verification failed",a1,{key:r,data:a,signature:n,algorithm:i})}async function ip(e,t,r,n,a){let i,o,{0:s,1:c,length:l}=e.split(".");if(5===l)if(void 0!==a)e=await a(e),{0:s,1:c,length:l}=e.split(".");else throw new nM("JWE decryption is not configured",{cause:e});if(3!==l)throw nJ("Invalid JWT",a1,e);try{i=JSON.parse(nK(nL(s)))}catch(e){throw nJ("failed to parse JWT Header body as base64url encoded JSON",a0,e)}if(!nF(i))throw nJ("JWT Header must be a top level object",a1,e);if(t(i),void 0!==i.crit)throw new nM('no JWT "crit" header parameter extensions are supported',{cause:{header:i}});try{o=JSON.parse(nK(nL(c)))}catch(e){throw nJ("failed to parse JWT Payload body as base64url encoded JSON",a0,e)}if(!nF(o))throw nJ("JWT Payload must be a top level object",a1,e);let u=n9()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw nJ('unexpected JWT "exp" (expiration time) claim type',a1,{claims:o});if(o.exp<=u-n)throw nJ('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',a4,{claims:o,now:u,tolerance:n,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw nJ('unexpected JWT "iat" (issued at) claim type',a1,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw nJ('unexpected JWT "iss" (issuer) claim type',a1,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw nJ('unexpected JWT "nbf" (not before) claim type',a1,{claims:o});if(o.nbf>u+n)throw nJ('unexpected JWT "nbf" (not before) claim value',a4,{claims:o,now:u,tolerance:n,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw nJ('unexpected JWT "aud" (audience) claim type',a1,{claims:o});return{header:i,claims:o,jwt:e}}async function ih(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new nM(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let a=await crypto.subtle.digest(n,nK(e));return nL(a.slice(0,a.byteLength/2))}async function iy(e,t,r,n){return t===await ih(e,r,n)}async function ib(e){if(e.bodyUsed)throw nP("form_post Request instances must contain a readable body",nR,{cause:e});return e.text()}async function im(e){if("POST"!==e.method)throw nP("form_post responses are expected to use the POST method",nR,{cause:e});if("application/x-www-form-urlencoded"!==aS(e))throw nP("form_post responses are expected to use the application/x-www-form-urlencoded content-type",nR,{cause:e});return ib(e)}function ig(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw nJ('unexpected JWT "alg" header parameter',a1,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw nJ('unexpected JWT "alg" header parameter',a1,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw nJ('unexpected JWT "alg" header parameter',a1,{header:n,expected:r,reason:"default value"});return}throw nJ('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function ix(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw nJ(`"${t}" parameter must be provided only once`,a1);return r}let iw=Symbol(),i_=Symbol();function iv(e,t,r,n){var a;if(n7(e),ae(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw nP('"parameters" must be an instance of URLSearchParams, or URL',nT);if(ix(r,"response"))throw nJ('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',a1,{parameters:r});let i=ix(r,"iss"),o=ix(r,"state");if(!i&&e.authorization_response_iss_parameter_supported)throw nJ('response parameter "iss" (issuer) missing',a1,{parameters:r});if(i&&i!==e.issuer)throw nJ('unexpected "iss" (issuer) response parameter value',a1,{expected:e.issuer,parameters:r});switch(n){case void 0:case i_:if(void 0!==o)throw nJ('unexpected "state" response parameter encountered',a1,{expected:void 0,parameters:r});break;case iw:break;default:if(nQ(n,'"expectedState" argument'),o!==n)throw nJ(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',a1,{expected:n,parameters:r})}if(ix(r,"error"))throw new au("authorization response from the server is an error",{cause:r});let s=ix(r,"id_token"),c=ix(r,"token");if(void 0!==s||void 0!==c)throw new nM("implicit and hybrid flows are not supported");return a=new URLSearchParams(r),aK.add(a),a}async function ik(e,t){let{ext:r,key_ops:n,use:a,...i}=t;return crypto.subtle.importKey("jwk",i,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nM("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function iE(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function iA(e,t=n1){let r;try{r=await e.json()}catch(r){throw t(e),nJ('failed to parse "response" body as JSON',a0,r)}if(!nF(r))throw nJ('"response" body must be a top level object',a1,{body:r});return r}let iS=Symbol(),iR=Symbol();async function iT(e,t,r){let{cookies:n,logger:a}=r,i=n[e],o=new Date;o.setTime(o.getTime()+9e5),a.debug(`CREATE_${e.toUpperCase()}`,{name:i.name,payload:t,COOKIE_TTL:900,expires:o});let s=await t0({...r.jwt,maxAge:900,token:{value:t},salt:i.name}),c={...i.options,expires:o};return{name:i.name,value:s,options:c}}async function iP(e,t,r){try{let{logger:n,cookies:a,jwt:i}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new A(`${e} cookie was missing`);let o=await t1({...i,token:t,salt:a[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new A(`${e} value could not be parsed`,{cause:t})}}function iC(e,t,r){let{logger:n,cookies:a}=t,i=a[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:i}),r.push({name:i.name,value:"",options:{...a[e].options,maxAge:0}})}function iO(e,t){return async function(r,n,a){let{provider:i,logger:o}=a;if(!i?.checks?.includes(e))return;let s=r?.[a.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await iP(t,s,a);return iC(t,a,n),c}}let iU={async create(e){let t=n5(),r=await n6(t);return{cookie:await iT("pkceCodeVerifier",t,e),value:r}},use:iO("pkce","pkceCodeVerifier")},i$="encodedState",ij={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new A("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:n5()},a=await t0({secret:e.jwt.secret,token:n,salt:i$,maxAge:900});return{cookie:await iT("state",a,e),value:a}},use:iO("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await t1({secret:t.jwt.secret,token:e,salt:i$});if(r)return r;throw Error("Invalid state")}catch(e){throw new A("State could not be decoded",{cause:e})}}},iI={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=n5();return{cookie:await iT("nonce",t,e),value:t}},use:iO("nonce","nonce")},iH="encodedWebauthnChallenge",iD={create:async(e,t,r)=>({cookie:await iT("webauthnChallenge",await t0({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:iH,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],a=await iP("webauthnChallenge",n,e),i=await t1({secret:e.jwt.secret,token:a,salt:iH});if(iC("webauthnChallenge",e,r),!i)throw new A("WebAuthn challenge was missing");return i}};function iW(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function iK(e,t,r){let n,a,i,{logger:o,provider:s}=r,{token:c,userinfo:l}=s;if(c?.url&&"authjs.dev"!==c.url.host||l?.url&&"authjs.dev"!==l.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:l?.url.toString()};else{let e=new URL(s.issuer),t=await nY(e,{[nC]:!0,[n$]:s[rf]});if(!(n=await n0(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:s.clientId,...s.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":a=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=iW(e),n=iW(t),a=btoa(`${r}:${n}`);return`Basic ${a}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var d;nQ(d=s.clientSecret,'"clientSecret"'),a=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":a=function(e,t){let r;nQ(e,'"clientSecret"');let n=void 0;return async(t,a,i,o)=>{r||=await crypto.subtle.importKey("raw",nK(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=at(t,a);n?.(s,c);let l=`${nL(nK(JSON.stringify(s)))}.${nL(nK(JSON.stringify(c)))}`,u=await crypto.subtle.sign(r.algorithm,r,nK(l));i.set("client_id",a.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",`${l}.${nL(new Uint8Array(u))}`)}}(s.clientSecret);break;case"private_key_jwt":a=function(e,t){var r;let{key:n,kid:a}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&nQ(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return nq(n,'"clientPrivateKey.key"'),async(e,r,i,o)=>{let s={alg:n3(n),kid:a},c=at(e,r);t?.[nj]?.(s,c),i.set("client_id",r.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",await ar(s,c,n))}}(s.token.clientPrivateKey,{[nj](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":a=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let f=[],p=await ij.use(t,f,r);try{i=iv(n,u,new URLSearchParams(e),s.checks.includes("state")?p:iw)}catch(e){if(e instanceof au){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new U("OAuth Provider returned an error",t)}throw e}let h=await iU.use(t,f,r),y=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(y=s.redirectProxyUrl);let b=await aM(n,u,a,i,y,h??"decoy",{[nC]:!0,[n$]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[rf]??fetch)(...e))});s.token?.conform&&(b=await s.token.conform(b.clone())??b);let m={},g="oidc"===s.type;if(s[rp])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let e=await b.clone().json();if(e.error){let t={providerId:s.id,...e};throw new U(`OAuth Provider returned an error: ${e.error}`,t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new eA("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:a}=e.split(".");if(5===a)throw new eA("Only JWTs using Compact JWS serialization can be decoded");if(3!==a)throw new eA("Invalid JWT");if(!n)throw new eA("JWTs must contain a payload");try{t=eb(n)}catch{throw new eA("Failed to base64url decode the payload")}try{r=JSON.parse(ed.decode(t))}catch{throw new eA("Failed to parse the decoded payload as JSON")}if(!eU(r))throw new eA("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(e,t)),a=await nY(r,{[n$]:s[rf]});n=await n0(r,a)}}}let x=await aF(n,u,b,{expectedNonce:await iI.use(t,f,r),requireIdToken:g});if(g){let t=aU(x);if(m=t,s[rp]&&"apple"===s.id)try{m.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await a_(n,u,x.access_token,{[n$]:s[rf],[nC]:!0});m=await aR(n,u,t.sub,e)}}else if(l?.request){let e=await l.request({tokens:x,provider:s});e instanceof Object&&(m=e)}else if(l?.url){let e=await a_(n,u,x.access_token,{[n$]:s[rf],[nC]:!0});m=await e.json()}else throw TypeError("No userinfo endpoint configured");return x.expires_in&&(x.expires_at=Math.floor(Date.now()/1e3)+Number(x.expires_in)),{...await iL(m,s,x,o),profile:m,cookies:f}}async function iL(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new $(r,{provider:t.id}))}}async function iM(e,t,r,n){let a=await iF(e,t,r),{cookie:i}=await iD.create(e,a.challenge,r);return{status:200,cookies:[...n??[],i],body:{action:"register",options:a},headers:{"Content-Type":"application/json"}}}async function iN(e,t,r,n){let a=await iq(e,t,r),{cookie:i}=await iD.create(e,a.challenge);return{status:200,cookies:[...n??[],i],body:{action:"authenticate",options:a},headers:{"Content-Type":"application/json"}}}async function iJ(e,t,r){let n,{adapter:a,provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new y("Invalid WebAuthn Authentication response");let s=iG(iV(o.id)),c=await a.getAuthenticator(s);if(!c)throw new y(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:l}=await iD.use(e,t.cookies,r);try{var u;let r=i.getRelayingParty(e,t);n=await i.simpleWebAuthn.verifyAuthenticationResponse({...i.verifyAuthenticationOptions,expectedChallenge:l,response:o,authenticator:{...u=c,credentialDeviceType:u.credentialDeviceType,transports:iX(u.transports),credentialID:iV(u.credentialID),credentialPublicKey:iV(u.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new z(e)}let{verified:d,authenticationInfo:f}=n;if(!d)throw new z("WebAuthn authentication response could not be verified");try{let{newCounter:e}=f;await a.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new m(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:f.newCounter})}`,e)}let p=await a.getAccount(c.providerAccountId,i.id);if(!p)throw new y(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let h=await a.getUser(p.userId);if(!h)throw new y(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:p.userId})}`);return{account:p,user:h}}async function iB(e,t,r){var n;let a,{provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new y("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await iD.use(e,t.cookies,r);if(!c)throw new y("Missing user registration data in WebAuthn challenge cookie");try{let r=i.getRelayingParty(e,t);a=await i.simpleWebAuthn.verifyRegistrationResponse({...i.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new z(e)}if(!a.verified||!a.registrationInfo)throw new z("WebAuthn registration response could not be verified");let l={providerAccountId:iG(a.registrationInfo.credentialID),provider:e.provider.id,type:i.type},u={providerAccountId:l.providerAccountId,counter:a.registrationInfo.counter,credentialID:iG(a.registrationInfo.credentialID),credentialPublicKey:iG(a.registrationInfo.credentialPublicKey),credentialBackedUp:a.registrationInfo.credentialBackedUp,credentialDeviceType:a.registrationInfo.credentialDeviceType,transports:(n=o.response.transports,n?.join(","))};return{user:c,account:l,authenticator:u}}async function iq(e,t,r){let{provider:n,adapter:a}=e,i=r&&r.id?await a.listAuthenticatorsByUserId(r.id):null,o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:o.id,allowCredentials:i?.map(e=>({id:iV(e.credentialID),type:"public-key",transports:iX(e.transports)}))})}async function iF(e,t,r){let{provider:n,adapter:a}=e,i=r.id?await a.listAuthenticatorsByUserId(r.id):null,o=ri(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:i?.map(e=>({id:iV(e.credentialID),type:"public-key",transports:iX(e.transports)}))})}function iz(e){let{provider:t,adapter:r}=e;if(!r)throw new R("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new L("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function iV(e){return new Uint8Array(Buffer.from(e,"base64"))}function iG(e){return Buffer.from(e).toString("base64")}function iX(e){return e?e.split(","):void 0}async function iY(e,t,r,n){if(!t.provider)throw new L("Callback route called without provider");let{query:a,body:i,method:o,headers:s}=e,{provider:c,adapter:l,url:u,callbackUrl:d,pages:f,jwt:p,events:h,callbacks:b,session:{strategy:m,maxAge:g},logger:w}=t,_="jwt"===m;try{if("oauth"===c.type||"oidc"===c.type){let o,s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?i:a;if(t.isOnRedirectProxy&&s?.state){let e=await ij.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return w.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let y=await iK(s,e.cookies,t);y.cookies.length&&n.push(...y.cookies),w.debug("authorization result",y);let{user:m,account:x,profile:v}=y;if(!m||!x||!v)return{redirect:`${u}/signin`,cookies:n};if(l){let{getUserByAccount:e}=l;o=await e({providerAccountId:x.providerAccountId,provider:c.id})}let k=await iZ({user:o??m,account:x,profile:v},t);if(k)return{redirect:k,cookies:n};let{user:E,session:A,isNewUser:S}=await nA(r.value,m,x,t);if(_){let e={name:E.name,email:E.email,picture:E.image,sub:E.id?.toString()},a=await b.jwt({token:e,user:E,account:x,profile:v,isNewUser:S,trigger:S?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await p.encode({...p,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:A.sessionToken,options:{...t.cookies.sessionToken.options,expires:A.expires}});if(await h.signIn?.({user:E,account:x,profile:v,isNewUser:S}),S&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=a?.token,i=a?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await l.useVerificationToken({identifier:i,token:await ra(`${e}${o}`)}),u=!!s,y=u&&s.expires.valueOf()<Date.now();if(!u||y||i&&s.identifier!==i)throw new N({hasInvite:u,expired:y});let{identifier:m}=s,x=await l.getUserByEmail(m)??{id:crypto.randomUUID(),email:m,emailVerified:null},w={providerAccountId:x.email,userId:x.id,type:"email",provider:c.id},v=await iZ({user:x,account:w},t);if(v)return{redirect:v,cookies:n};let{user:k,session:E,isNewUser:A}=await nA(r.value,x,w,t);if(_){let e={name:k.name,email:k.email,picture:k.image,sub:k.id?.toString()},a=await b.jwt({token:e,user:k,account:w,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await p.encode({...p,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:E.sessionToken,options:{...t.cookies.sessionToken.options,expires:E.expires}});if(await h.signIn?.({user:k,account:w,isNewUser:A}),A&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===o){let e=i??{};Object.entries(a??{}).forEach(([e,t])=>u.searchParams.set(e,t));let l=await c.authorize(e,new Request(u,{headers:s,method:o,body:JSON.stringify(i)}));if(l)l.id=l.id?.toString()??crypto.randomUUID();else throw new k;let f={providerAccountId:l.id,type:"credentials",provider:c.id},y=await iZ({user:l,account:f,credentials:e},t);if(y)return{redirect:y,cookies:n};let m={name:l.name,email:l.email,picture:l.image,sub:l.id},x=await b.jwt({token:m,user:l,account:f,isNewUser:!1,trigger:"signIn"});if(null===x)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await p.encode({...p,token:x,salt:e}),i=new Date;i.setTime(i.getTime()+1e3*g);let o=r.chunk(a,{expires:i});n.push(...o)}return await h.signIn?.({user:l,account:f}),{redirect:d,cookies:n}}else if("webauthn"===c.type&&"POST"===o){let a,i,o,s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new y("Invalid action parameter");let c=iz(t);switch(s){case"authenticate":{let t=await iJ(c,e,n);a=t.user,i=t.account;break}case"register":{let r=await iB(t,e,n);a=r.user,i=r.account,o=r.authenticator}}await iZ({user:a,account:i},t);let{user:l,isNewUser:u,session:m,account:x}=await nA(r.value,a,i,t);if(!x)throw new y("Error creating or finding account");if(o&&l.id&&await c.adapter.createAuthenticator({...o,userId:l.id}),_){let e={name:l.name,email:l.email,picture:l.image,sub:l.id?.toString()},a=await b.jwt({token:e,user:l,account:x,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await p.encode({...p,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:m.sessionToken,options:{...t.cookies.sessionToken.options,expires:m.expires}});if(await h.signIn?.({user:l,account:x,isNewUser:u}),u&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new L(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof y)throw t;let e=new x(t,{provider:c.id});throw w.debug("callback route error details",{method:o,query:a,body:i}),e}}async function iZ(e,t){let r,{signIn:n,redirect:a}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof y)throw e;throw new g(e)}if(!r)throw new g("AccessDenied");if("string"==typeof r)return await a({url:r,baseUrl:t.url.origin})}async function iQ(e,t,r,n,a){let{adapter:i,jwt:o,events:s,callbacks:c,logger:l,session:{strategy:u,maxAge:d}}=e,f={body:null,headers:{"Content-Type":"application/json",...!n&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},p=t.value;if(!p)return f;if("jwt"===u){try{let r=e.cookies.sessionToken.name,i=await o.decode({...o,token:p,salt:r});if(!i)throw Error("Invalid JWT");let l=await c.jwt({token:i,...n&&{trigger:"update"},session:a}),u=nE(d);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:u.toISOString()},n=await c.session({session:e,token:l});f.body=n;let a=await o.encode({...o,token:l,salt:r}),i=t.chunk(a,{expires:u});f.cookies?.push(...i),await s.session?.({session:n,token:l})}else f.cookies?.push(...t.clean())}catch(e){l.error(new S(e)),f.cookies?.push(...t.clean())}return f}try{let{getSessionAndUser:r,deleteSession:o,updateSession:l}=i,u=await r(p);if(u&&u.session.expires.valueOf()<Date.now()&&(await o(p),u=null),u){let{user:t,session:r}=u,i=e.session.updateAge,o=r.expires.valueOf()-1e3*d+1e3*i,h=nE(d);o<=Date.now()&&await l({sessionToken:p,expires:h});let y=await c.session({session:{...r,user:t},user:t,newSession:a,...n?{trigger:"update"}:{}});f.body=y,f.cookies?.push({name:e.cookies.sessionToken.name,value:p,options:{...e.cookies.sessionToken.options,expires:h}}),await s.session?.({session:y})}else p&&f.cookies?.push(...t.clean())}catch(e){l.error(new j(e))}return f}async function i0(e,t){let r,n,{logger:a,provider:i}=t,o=i.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(i.issuer),t=await nY(e,{[n$]:i[rf],[nC]:!0}),r=await n0(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${e}`)});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=i.callbackUrl;!t.isOnRedirectProxy&&i.redirectProxyUrl&&(c=i.redirectProxyUrl,n=i.callbackUrl,a.debug("using redirect proxy",{redirect_uri:c,data:n}));let l=Object.assign({response_type:"code",client_id:i.clientId,redirect_uri:c,...i.authorization?.params},Object.fromEntries(i.authorization?.url.searchParams??[]),e);for(let e in l)s.set(e,l[e]);let u=[];i.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await ij.create(t,n);if(d&&(s.set("state",d.value),u.push(d.cookie)),i.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===i.type&&(i.checks=["nonce"]);else{let{value:e,cookie:r}=await iU.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),u.push(r)}let f=await iI.create(t);return f&&(s.set("nonce",f.value),u.push(f.cookie)),"oidc"!==i.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),a.debug("authorization url is ready",{url:o,cookies:u,provider:i}),{redirect:o.toString(),cookies:u}}async function i1(e,t){let r,{body:n}=e,{provider:a,callbacks:i,adapter:o}=t,s=(a.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},l=await o.getUserByEmail(s)??c,u={providerAccountId:s,userId:l.id,type:"email",provider:a.id};try{r=await i.signIn({user:l,account:u,email:{verificationRequest:!0}})}catch(e){throw new g(e)}if(!r)throw new g("AccessDenied");if("string"==typeof r)return{redirect:await i.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:f}=t,p=await a.generateVerificationToken?.()??ri(32),h=new Date(Date.now()+(a.maxAge??86400)*1e3),y=a.secret??t.secret,b=new URL(t.basePath,t.url.origin),m=a.sendVerificationRequest({identifier:s,token:p,expires:h,url:`${b}/callback/${a.id}?${new URLSearchParams({callbackUrl:d,token:p,email:s})}`,provider:a,theme:f,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),x=o.createVerificationToken?.({identifier:s,token:await ra(`${p}${y}`),expires:h});return await Promise.all([m,x]),{redirect:`${b}/verify-request?${new URLSearchParams({provider:a.id,type:a.type})}`}}async function i2(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:a}=await i0(e.query,r);return a&&t.push(...a),{redirect:n,cookies:t}}case"email":return{...await i1(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function i5(e,t,r){let{jwt:n,events:a,callbackUrl:i,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:i,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await a.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await a.signOut?.({session:e})}}catch(e){o.error(new D(e))}return e.push(...t.clean()),{redirect:i,cookies:e}}async function i6(e,t){let{adapter:r,jwt:n,session:{strategy:a}}=e,i=t.value;if(!i)return null;if("jwt"===a){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:i,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(i);if(e)return e.user}return null}async function i3(e,t,r,n){let a=iz(t),{provider:i}=a,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await i6(t,r),c=s?{user:s,exists:!0}:await i.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:n,exists:a=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===a)return"register";break;case void 0:if(!t)if(!n)return"authenticate";else if(a)return"authenticate";else return"register"}return null}(o,!!s,c)){case"authenticate":return iN(a,e,l,n);case"register":if("string"==typeof l?.email)return iM(a,e,l,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function i8(e,t){let{action:r,providerId:n,error:a,method:i}=e,o=t.skipCSRFCheck===ru,{options:s,cookies:c}=await rx({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===i,csrfDisabled:o}),l=new h(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===i){let t=nk({...s,query:e.query,cookies:c});switch(r){case"callback":return await iY(e,s,l,c);case"csrf":return t.csrf(o,s,c);case"error":return t.error(a);case"providers":return t.providers(s.providers);case"session":return await iQ(s,l,c);case"signin":return t.signin(n,a);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await i3(e,s,l,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&rs(r,t),await iY(e,s,l,c);case"session":return rs(r,t),await iQ(s,l,c,!0,e.body?.data);case"signin":return rs(r,t),await i2(e,c,s);case"signout":return rs(r,t),await i5(c,l,s)}}throw new W(`Cannot handle action: ${r}`)}function i4(e,t,r,n,a){let i,o=a?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)i=new URL(s),o&&"/"!==o&&"/"!==i.pathname&&(i.pathname!==o&&t4(a).warn("env-url-basepath-mismatch"),i.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",a=n.endsWith(":")?n:n+":";i=new URL(`${a}//${e}`)}let c=i.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function i9(e,t){let r=t4(t),n=await rr(e,t);if(!n)return Response.json("Bad request.",{status:400});let a=function(e,t){let{url:r}=e,n=[];if(!X&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new M(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new C("Please define a `secret`");let a=e.query?.callbackUrl;if(a&&!Y(a,r.origin))return new v(`Invalid callback URL. Received: ${a}`);let{callbackUrl:i}=p(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??i.name];if(o&&!Y(o,r.origin))return new v(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:n,userinfo:a}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof a||a?.url||(e="userinfo"):e="token":e="authorization",e)return new E(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)Z=!0;else if("email"===t.type)Q=!0;else if("webauthn"===t.type){var c;if(ee=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new y(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new q("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new F(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(Z){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new K("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new P("Must define an authorize() handler to use credentials authentication provider")}let{adapter:l,session:u}=t,d=[];if(Q||u?.strategy==="database"||!u?.strategy&&l)if(Q){if(!l)return new R("Email login requires an adapter");d.push(...et)}else{if(!l)return new R("Database session requires an adapter");d.push(...er)}if(ee){if(!t.experimental?.enableWebAuthn)return new G("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!l)return new R("WebAuthn requires an adapter");d.push(...en)}if(l){let e=d.filter(e=>!(e in l));if(e.length)return new T(`Required adapter methods were missing: ${e.join(", ")}`)}return X||(X=!0),n}(n,t);if(Array.isArray(a))a.forEach(r.warn);else if(a){if(r.error(a),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:i}=t,o=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new w(`The error page ${e?.error} should not require authentication`)),rn(nk({theme:i}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let i=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===rd;try{let e=await i8(n,t);if(o)return e;let r=rn(e),a=r.headers.get("Location");if(!i||!a)return r;return Response.json({url:a},{headers:r.headers})}catch(d){r.error(d);let a=d instanceof y;if(a&&o&&!i)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:d instanceof y&&B.has(d.type)?d.type:"Configuration"});d instanceof k&&s.set("code",d.code);let c=a&&d.kind||"error",l=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,u=`${n.url.origin}${l}?${s}`;if(i)return Response.json({url:u});return Response.redirect(u)}}var i7=r(2190);function oe(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:a}=e.nextUrl;return new i7.NextRequest(n.replace(a,r),e)}function ot(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||t4(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),a=e[`AUTH_${n}_ID`],i=e[`AUTH_${n}_SECRET`],o=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:a,clientSecret:i,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=a),c.clientSecret??(c.clientSecret=i),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}var or=r(9933),on=r(6280);async function oa(e,t){return i9(new Request(i4("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function oi(e){return"function"==typeof e}function oo(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,on.b)(),n=await e(void 0);return t?.(n),oa(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],a=r[1],i=await e(n);return t?.(i),os([n,a],i)}if(oi(r[0])){let n=r[0];return async(...r)=>{let a=await e(r[0]);return t?.(a),os(r,a,n)}}let n="req"in r[0]?r[0].req:r[0],a="res"in r[0]?r[0].res:r[1],i=await e(n);return t?.(i),oa(new Headers(n.headers),i).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in a?a.headers.append("set-cookie",t):a.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,on.b)()).then(t=>oa(t,e).then(e=>e.json()));if(t[0]instanceof Request)return os([t[0],t[1]],e);if(oi(t[0])){let r=t[0];return async(...t)=>os(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return oa(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function os(e,t,r){let n=oe(e[0]),a=await oa(n.headers,t),i=await a.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:n,auth:i}));let s=i7.NextResponse.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),a=Object.values(r.pages??{});return(oc.has(n)||a.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)n.auth=i,s=await r(n,e[1])??i7.NextResponse.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=i7.NextResponse.redirect(t)}}let c=new Response(s?.body,s);for(let e of a.headers.getSetCookie())c.headers.append("set-cookie",e);return c}r(6294);let oc=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var ol=r(7576);async function ou(e,t={},r,n){let a=new Headers(await (0,on.b)()),{redirect:i=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??a.get("Referer")??"/",l=i4("signin",a.get("x-forwarded-proto"),a,process.env,n);if(!e)return l.searchParams.append("callbackUrl",c),i&&(0,ol.redirect)(l.toString()),l.toString();let u=`${l}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,a=r?.id??n.id;if(a===e){d={id:a,type:r?.type??n.type};break}}if(!d.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return i&&(0,ol.redirect)(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),a.set("Content-Type","application/x-www-form-urlencoded");let f=new Request(u,{method:"POST",headers:a,body:new URLSearchParams({...s,callbackUrl:c})}),p=await i9(f,{...n,raw:rd,skipCSRFCheck:ru}),h=await (0,or.U)();for(let e of p?.cookies??[])h.set(e.name,e.value,e.options);let y=(p instanceof Response?p.headers.get("Location"):p.redirect)??u;return i?(0,ol.redirect)(y):y}async function od(e,t){let r=new Headers(await (0,on.b)());r.set("Content-Type","application/x-www-form-urlencoded");let n=i4("signout",r.get("x-forwarded-proto"),r,process.env,t),a=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),i=new Request(n,{method:"POST",headers:r,body:a}),o=await i9(i,{...t,raw:rd,skipCSRFCheck:ru}),s=await (0,or.U)();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?(0,ol.redirect)(o.redirect):o}async function of(e,t){let r=new Headers(await (0,on.b)());r.set("Content-Type","application/json");let n=new Request(i4("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),a=await i9(n,{...t,raw:rd,skipCSRFCheck:ru}),i=await (0,or.U)();for(let e of a?.cookies??[])i.set(e.name,e.value,e.options);return a.body}function op(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return ot(r),i9(oe(t),r)};return{handlers:{GET:t,POST:t},auth:oo(e,e=>ot(e)),signIn:async(t,r,n)=>{let a=await e(void 0);return ot(a),ou(t,r,n,a)},signOut:async t=>{let r=await e(void 0);return ot(r),od(t,r)},unstable_update:async t=>{let r=await e(void 0);return ot(r),of(t,r)}}}ot(e);let t=t=>i9(oe(t),e);return{handlers:{GET:t,POST:t},auth:oo(e),signIn:(t,r,n)=>ou(t,r,n,e),signOut:t=>od(t,e),unstable_update:t=>of(t,e)}}},8704:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return a},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return o},isHTTPAccessFallbackError:function(){return i}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),a="NEXT_HTTP_ERROR_FALLBACK";function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===a&&n.has(Number(r))}function o(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8976:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9026:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return a},RedirectType:function(){return i},isRedirectError:function(){return o}});let n=r(2836),a="NEXT_REDIRECT";var i=function(e){return e.push="push",e.replace="replace",e}({});function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,i]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===a&&("replace"===i||"push"===i)&&"string"==typeof o&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9933:(e,t,r)=>{Object.defineProperty(t,"U",{enumerable:!0,get:function(){return f}});let n=r(4069),a=r(3158),i=r(9294),o=r(3033),s=r(4971),c=r(23),l=r(8388),u=r(6926),d=(r(4523),r(8719));function f(){let e="cookies",t=i.workAsyncStorage.getStore(),r=o.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return h(n.RequestCookiesAdapter.seal(new a.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new c.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)if("prerender"===r.type){var u=t.route,f=r;let e=p.get(f);if(e)return e;let n=(0,l.makeHangingPromise)(f.renderSignal,"`cookies()`");return p.set(f,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=m(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},size:{get(){let e="`cookies().size`",t=m(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${y(arguments[0])})\``;let t=m(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${y(arguments[0])})\``;let t=m(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${y(arguments[0])})\``;let t=m(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${y(t)}, ...)\``:"`cookies().set(...)`"}let t=m(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${y(arguments[0])})\``:`\`cookies().delete(${y(arguments[0])}, ...)\``;let t=m(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},clear:{value:function(){let e="`cookies().clear()`",t=m(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},toString:{value:function(){let e="`cookies().toString()`",t=m(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}}}),n}else"prerender-ppr"===r.type?(0,s.postponeWithTracking)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,s.throwToInterruptStaticGeneration)(e,t,r);(0,s.trackDynamicDataInDynamicRender)(t,r)}let b=(0,o.getExpectedRequestStore)(e);return h((0,n.areCookiesMutableInCurrentPhase)(b)?b.userspaceMutableCookies:b.cookies)}let p=new WeakMap;function h(e){let t=p.get(e);if(t)return t;let r=Promise.resolve(e);return p.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):g.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):x.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function y(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let b=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(m);function m(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function g(){return this.getAll().map(e=>[e.name,e]).values()}function x(e){for(let e of this.getAll())this.delete(e.name);return e}}};