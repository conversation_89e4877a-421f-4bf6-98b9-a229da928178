{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "6fc6ae3f20b7402d7855b12646d3c8d9", "previewModeSigningKey": "b61c67c943ea0276636c3c415318019e7aff0ae9afd9aa5b15203c641802dc9b", "previewModeEncryptionKey": "22f69c0f1ae841a9270d2aed559d9c7aa818a258f08c57a3e154737b01ff4cbc"}}