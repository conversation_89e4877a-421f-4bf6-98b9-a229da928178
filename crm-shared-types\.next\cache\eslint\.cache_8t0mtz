[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\index.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\auth.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\service-clients.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\assignments.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\courses.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\index.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\kpis.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\leads.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\payments.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\system.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\users.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\data-sync.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\webhooks.ts": "15"}, {"size": 393, "mtime": 1750448012916, "results": "16", "hashOfConfig": "17"}, {"size": 776, "mtime": 1750448088581, "results": "18", "hashOfConfig": "17"}, {"size": 1179, "mtime": 1750490870455, "results": "19", "hashOfConfig": "17"}, {"size": 4879, "mtime": 1750479424984, "results": "20", "hashOfConfig": "17"}, {"size": 9783, "mtime": 1750490776778, "results": "21", "hashOfConfig": "17"}, {"size": 3179, "mtime": 1750448143049, "results": "22", "hashOfConfig": "17"}, {"size": 2933, "mtime": 1750448126681, "results": "23", "hashOfConfig": "17"}, {"size": 2377, "mtime": 1750448214501, "results": "24", "hashOfConfig": "17"}, {"size": 3025, "mtime": 1750448178915, "results": "25", "hashOfConfig": "17"}, {"size": 1877, "mtime": 1750448112529, "results": "26", "hashOfConfig": "17"}, {"size": 3402, "mtime": 1750448160746, "results": "27", "hashOfConfig": "17"}, {"size": 3685, "mtime": 1750448199461, "results": "28", "hashOfConfig": "17"}, {"size": 2157, "mtime": 1750448101387, "results": "29", "hashOfConfig": "17"}, {"size": 9191, "mtime": 1750490922894, "results": "30", "hashOfConfig": "17"}, {"size": 8783, "mtime": 1750490944697, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ba3sxi", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\service-clients.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\assignments.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\courses.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\kpis.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\leads.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\payments.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\system.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\users.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\data-sync.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\webhooks.ts", [], ["77"], {"ruleId": "78", "severity": 2, "message": "79", "line": 244, "column": 20, "nodeType": "80", "messageId": "81", "endLine": 244, "endColumn": 37, "suppressions": "82"}, "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["83"], {"kind": "84", "justification": "85"}, "directive", ""]