// Notification System for CRM Services

export interface NotificationTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  type: 'email' | 'sms' | 'push' | 'system';
  variables: string[];
  isActive: boolean;
}

export interface Notification {
  id: string;
  type: 'email' | 'sms' | 'push' | 'system';
  recipient: string;
  subject?: string;
  message: string;
  data?: Record<string, any>;
  status: 'pending' | 'sent' | 'failed' | 'delivered';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  scheduledAt?: Date;
  sentAt?: Date;
  deliveredAt?: Date;
  failureReason?: string;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface SystemAnnouncement {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  targetAudience: 'all' | 'staff' | 'students' | 'admins';
  isActive: boolean;
  startDate: Date;
  endDate?: Date;
  createdBy: string;
  createdAt: Date;
}

export interface MessageThread {
  id: string;
  subject: string;
  participants: string[];
  lastMessageAt: Date;
  isArchived: boolean;
  createdAt: Date;
}

export interface Message {
  id: string;
  threadId: string;
  senderId: string;
  content: string;
  attachments?: string[];
  isRead: boolean;
  sentAt: Date;
}

// Notification Manager
export class NotificationManager {
  private templates: Map<string, NotificationTemplate> = new Map();
  private notifications: Map<string, Notification> = new Map();
  private announcements: Map<string, SystemAnnouncement> = new Map();

  constructor(private emailProvider?: EmailProvider) {}

  // Template Management
  addTemplate(template: NotificationTemplate): void {
    this.templates.set(template.id, template);
  }

  getTemplate(templateId: string): NotificationTemplate | undefined {
    return this.templates.get(templateId);
  }

  // Send notification using template
  async sendNotification(
    templateId: string,
    recipient: string,
    variables: Record<string, any>,
    options: {
      priority?: 'low' | 'normal' | 'high' | 'urgent';
      scheduledAt?: Date;
    } = {}
  ): Promise<string> {
    const template = this.getTemplate(templateId);
    if (!template || !template.isActive) {
      throw new Error(`Template ${templateId} not found or inactive`);
    }

    const notification: Notification = {
      id: this.generateId(),
      type: template.type,
      recipient,
      subject: this.interpolateTemplate(template.subject, variables),
      message: this.interpolateTemplate(template.body, variables),
      data: variables,
      status: 'pending',
      priority: options.priority || 'normal',
      scheduledAt: options.scheduledAt,
      retryCount: 0,
      maxRetries: 3,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.notifications.set(notification.id, notification);

    // Send immediately if not scheduled
    if (!options.scheduledAt) {
      await this.processNotification(notification.id);
    }

    return notification.id;
  }

  // Send direct notification
  async sendDirectNotification(
    type: 'email' | 'sms' | 'push' | 'system',
    recipient: string,
    subject: string,
    message: string,
    options: {
      priority?: 'low' | 'normal' | 'high' | 'urgent';
      scheduledAt?: Date;
      data?: Record<string, any>;
    } = {}
  ): Promise<string> {
    const notification: Notification = {
      id: this.generateId(),
      type,
      recipient,
      subject,
      message,
      data: options.data,
      status: 'pending',
      priority: options.priority || 'normal',
      scheduledAt: options.scheduledAt,
      retryCount: 0,
      maxRetries: 3,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.notifications.set(notification.id, notification);

    // Send immediately if not scheduled
    if (!options.scheduledAt) {
      await this.processNotification(notification.id);
    }

    return notification.id;
  }

  // Process notification
  private async processNotification(notificationId: string): Promise<void> {
    const notification = this.notifications.get(notificationId);
    if (!notification) {
      throw new Error(`Notification ${notificationId} not found`);
    }

    try {
      notification.status = 'pending';
      notification.updatedAt = new Date();

      switch (notification.type) {
        case 'email':
          await this.sendEmail(notification);
          break;
        case 'sms':
          await this.sendSMS(notification);
          break;
        case 'push':
          await this.sendPushNotification(notification);
          break;
        case 'system':
          await this.sendSystemNotification(notification);
          break;
      }

      notification.status = 'sent';
      notification.sentAt = new Date();
    } catch (error) {
      notification.status = 'failed';
      notification.failureReason = error instanceof Error ? error.message : 'Unknown error';
      notification.retryCount++;

      // Schedule retry if under max retries
      if (notification.retryCount < notification.maxRetries) {
        setTimeout(() => {
          this.processNotification(notificationId);
        }, this.getRetryDelay(notification.retryCount));
      }
    }

    notification.updatedAt = new Date();
  }

  // Send email
  private async sendEmail(notification: Notification): Promise<void> {
    if (!this.emailProvider) {
      throw new Error('Email provider not configured');
    }

    await this.emailProvider.sendEmail({
      to: notification.recipient,
      subject: notification.subject || 'Notification',
      body: notification.message,
      data: notification.data,
    });
  }

  // Send SMS (placeholder)
  private async sendSMS(notification: Notification): Promise<void> {
    // Implement SMS sending logic
    console.log(`SMS to ${notification.recipient}: ${notification.message}`);
  }

  // Send push notification (placeholder)
  private async sendPushNotification(notification: Notification): Promise<void> {
    // Implement push notification logic
    console.log(`Push to ${notification.recipient}: ${notification.message}`);
  }

  // Send system notification
  private async sendSystemNotification(notification: Notification): Promise<void> {
    // Store in system notification queue
    console.log(`System notification for ${notification.recipient}: ${notification.message}`);
  }

  // Template interpolation
  private interpolateTemplate(template: string, variables: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] || match;
    });
  }

  // Get retry delay
  private getRetryDelay(retryCount: number): number {
    return Math.min(1000 * Math.pow(2, retryCount), 30000); // Max 30 seconds
  }

  // Generate unique ID
  private generateId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Announcement Management
  createAnnouncement(announcement: Omit<SystemAnnouncement, 'id' | 'createdAt'>): string {
    const id = this.generateId();
    const newAnnouncement: SystemAnnouncement = {
      ...announcement,
      id,
      createdAt: new Date(),
    };

    this.announcements.set(id, newAnnouncement);
    return id;
  }

  getActiveAnnouncements(audience?: 'all' | 'staff' | 'students' | 'admins'): SystemAnnouncement[] {
    const now = new Date();
    return Array.from(this.announcements.values())
      .filter(announcement => 
        announcement.isActive &&
        announcement.startDate <= now &&
        (!announcement.endDate || announcement.endDate >= now) &&
        (!audience || announcement.targetAudience === 'all' || announcement.targetAudience === audience)
      );
  }

  // Get notification status
  getNotificationStatus(notificationId: string): Notification | undefined {
    return this.notifications.get(notificationId);
  }

  // Get notification statistics
  getNotificationStats(): {
    total: number;
    pending: number;
    sent: number;
    failed: number;
    byType: Record<string, number>;
  } {
    const notifications = Array.from(this.notifications.values());
    const stats = {
      total: notifications.length,
      pending: notifications.filter(n => n.status === 'pending').length,
      sent: notifications.filter(n => n.status === 'sent').length,
      failed: notifications.filter(n => n.status === 'failed').length,
      byType: {} as Record<string, number>,
    };

    notifications.forEach(n => {
      stats.byType[n.type] = (stats.byType[n.type] || 0) + 1;
    });

    return stats;
  }
}

// Email Provider Interface
export interface EmailProvider {
  sendEmail(email: {
    to: string;
    subject: string;
    body: string;
    data?: Record<string, any>;
  }): Promise<void>;
}

// Simple Email Provider Implementation
export class SimpleEmailProvider implements EmailProvider {
  constructor(
    private config: {
      smtpHost: string;
      smtpPort: number;
      username: string;
      password: string;
      fromEmail: string;
    }
  ) {}

  async sendEmail(email: {
    to: string;
    subject: string;
    body: string;
    data?: Record<string, any>;
  }): Promise<void> {
    // In a real implementation, use nodemailer or similar
    console.log(`Email sent to ${email.to}: ${email.subject}`);
    console.log(`Body: ${email.body}`);
  }
}

// Export factory function
export function createNotificationManager(emailProvider?: EmailProvider): NotificationManager {
  return new NotificationManager(emailProvider);
}
