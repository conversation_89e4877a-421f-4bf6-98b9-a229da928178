(()=>{var e={};e.id=135,e.ids=[135],e.modules={823:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>N,routeModule:()=>f,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var a={};t.r(a),t.d(a,{DELETE:()=>w,GET:()=>m,PUT:()=>y});var s=t(6559),n=t(8088),i=t(7719),o=t(2190),d=t(2909),u=t(5069),c=t(6330),p=t(5697);let l=p.z.object({amount:p.z.number().positive().optional(),paymentDate:p.z.string().transform(e=>new Date(e)).optional(),paymentMethod:p.z.nativeEnum(c.PaymentMethod).optional(),description:p.z.string().optional(),notes:p.z.string().optional(),status:p.z.nativeEnum(c.PaymentStatus).optional()});async function m(e,{params:r}){try{let t=await (0,d.j2)();if(!t?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{id:a}=await r,s=await u.z.paymentRecord.findUnique({where:{id:a},include:{recordedByUser:{select:{firstName:!0,lastName:!0,email:!0}},verifiedByUser:{select:{firstName:!0,lastName:!0,email:!0}},financialTransactions:{include:{performedByUser:{select:{firstName:!0,lastName:!0,email:!0}}},orderBy:{timestamp:"desc"}}}});if(!s)return o.NextResponse.json({error:"Payment not found"},{status:404});return await u.z.auditLog.create({data:{userId:t.user.id,action:"VIEW_PAYMENT",resourceType:"PAYMENT_RECORD",resourceId:s.id,ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Error fetching payment:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e,{params:r}){try{let t=await (0,d.j2)();if(!t?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{id:a}=await r,s=await e.json(),n=l.parse(s),i=await u.z.paymentRecord.findUnique({where:{id:a}});if(!i)return o.NextResponse.json({error:"Payment not found"},{status:404});let p=await u.z.paymentRecord.update({where:{id:a},data:n,include:{recordedByUser:{select:{firstName:!0,lastName:!0,email:!0}},verifiedByUser:{select:{firstName:!0,lastName:!0,email:!0}}}});return await u.z.financialTransaction.create({data:{paymentRecordId:p.id,transactionType:c.TransactionType.adjustment,amount:n.amount||i.amount,description:`Payment updated: ${n.description||"No description"}`,performedBy:t.user.id,ipAddress:"127.0.0.1"}}),await u.z.auditLog.create({data:{userId:t.user.id,action:"UPDATE_PAYMENT",resourceType:"PAYMENT_RECORD",resourceId:p.id,oldValues:i,newValues:n,ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:p})}catch(e){if(e instanceof p.z.ZodError)return o.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error updating payment:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function w(e,{params:r}){try{let t=await (0,d.j2)();if(!t?.user||"admin"!==t.user.role)return o.NextResponse.json({error:"Forbidden"},{status:403});let{id:a}=await r,s=await u.z.paymentRecord.findUnique({where:{id:a}});if(!s)return o.NextResponse.json({error:"Payment not found"},{status:404});return await u.z.financialTransaction.deleteMany({where:{paymentRecordId:a}}),await u.z.paymentRecord.delete({where:{id:a}}),await u.z.auditLog.create({data:{userId:t.user.id,action:"DELETE_PAYMENT",resourceType:"PAYMENT_RECORD",resourceId:a,oldValues:s,ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,message:"Payment record deleted successfully"})}catch(e){return console.error("Error deleting payment:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}p.z.object({verified:p.z.boolean(),notes:p.z.string().optional()});let f=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/payments/[id]/route",pathname:"/api/payments/[id]",filename:"route",bundlePath:"app/api/payments/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\payments\\[id]\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:x}=f;function N(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>d,j2:()=>u});var a=t(8643),s=t(189),n=t(6467),i=t(5069),o=t(5663);let{handlers:d,auth:u,signIn:c,signOut:p}=(0,a.Ay)({adapter:(0,n.y)(i.z),session:{strategy:"jwt",maxAge:1800},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"},mfaCode:{label:"MFA Code",type:"text"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await i.z.adminUser.findUnique({where:{email:e.email}});if(!r||!r.isActive||!await o.Ay.compare(e.password,r.passwordHash))return null;return r.mfaEnabled&&!e.mfaCode&&console.warn("MFA required but not implemented yet"),await i.z.adminUser.update({where:{id:r.id},data:{lastLogin:new Date}}),await i.z.auditLog.create({data:{userId:r.id,action:"LOGIN",resourceType:"AUTH",resourceId:r.id,newValues:{timestamp:new Date,success:!0},ipAddress:"127.0.0.1",userAgent:"Unknown"}}),{id:r.id,email:r.email,name:`${r.firstName} ${r.lastName}`,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.id=r.id),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.role=r.role),e),async authorized({auth:e,request:{nextUrl:r}}){let t=!!e?.user,a=r.pathname.startsWith("/dashboard"),s=r.pathname.startsWith("/auth");return a?!!t:!s||!t||Response.redirect(new URL("/dashboard",r))}},events:{async signOut({token:e}){e?.id&&await i.z.auditLog.create({data:{userId:e.id,action:"LOGOUT",resourceType:"AUTH",resourceId:e.id,newValues:{timestamp:new Date},ipAddress:"127.0.0.1",userAgent:"Unknown"}})}}}),{GET:l,POST:m}=d},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,r,t)=>{"use strict";t.d(r,{db:()=>n,z:()=>s});var a=t(6330);let s=globalThis.prisma??new a.PrismaClient({log:["query","error","warn"]}),n=s},5511:e=>{"use strict";e.exports=require("crypto")},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,199,828,697],()=>t(823));module.exports=a})();