// Individual Payment Record API - Admin Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { PaymentMethod, PaymentStatus, TransactionType } from "@prisma/client";
import { z } from "zod";

const updatePaymentSchema = z.object({
  amount: z.number().positive().optional(),
  paymentDate: z.string().transform((str) => new Date(str)).optional(),
  paymentMethod: z.nativeEnum(PaymentMethod).optional(),
  description: z.string().optional(),
  notes: z.string().optional(),
  status: z.nativeEnum(PaymentStatus).optional(),
});

const verifyPaymentSchema = z.object({
  verified: z.boolean(),
  notes: z.string().optional(),
});

// GET /api/payments/[id] - Get specific payment record
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const payment = await prisma.paymentRecord.findUnique({
      where: { id },
      include: {
        recordedByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        verifiedByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        financialTransactions: {
          include: {
            performedByUser: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
          orderBy: { timestamp: "desc" },
        },
      },
    });

    if (!payment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    // Log audit trail
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: "VIEW_PAYMENT",
        resourceType: "PAYMENT_RECORD",
        resourceId: payment.id,
        ipAddress: "127.0.0.1", // TODO: Get real IP
        userAgent: request.headers.get("user-agent") || "Unknown",
      },
    });

    return NextResponse.json({
      success: true,
      data: payment,
    });
  } catch (error) {
    console.error("Error fetching payment:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/payments/[id] - Update payment record
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const body = await request.json();
    const validatedData = updatePaymentSchema.parse(body);

    // Get existing payment for audit trail
    const existingPayment = await prisma.paymentRecord.findUnique({
      where: { id },
    });

    if (!existingPayment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    const updatedPayment = await prisma.paymentRecord.update({
      where: { id },
      data: validatedData,
      include: {
        recordedByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        verifiedByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // Create financial transaction record
    await prisma.financialTransaction.create({
      data: {
        paymentRecordId: updatedPayment.id,
        transactionType: TransactionType.adjustment,
        amount: validatedData.amount || existingPayment.amount,
        description: `Payment updated: ${validatedData.description || "No description"}`,
        performedBy: session.user.id,
        ipAddress: "127.0.0.1", // TODO: Get real IP
      },
    });

    // Log audit trail
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: "UPDATE_PAYMENT",
        resourceType: "PAYMENT_RECORD",
        resourceId: updatedPayment.id,
        oldValues: existingPayment,
        newValues: validatedData,
        ipAddress: "127.0.0.1", // TODO: Get real IP
        userAgent: request.headers.get("user-agent") || "Unknown",
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedPayment,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating payment:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/payments/[id] - Delete payment record (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user || session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id } = await params;
    const existingPayment = await prisma.paymentRecord.findUnique({
      where: { id },
    });

    if (!existingPayment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    // Delete related financial transactions first
    await prisma.financialTransaction.deleteMany({
      where: { paymentRecordId: id },
    });

    // Delete the payment record
    await prisma.paymentRecord.delete({
      where: { id },
    });

    // Log audit trail
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: "DELETE_PAYMENT",
        resourceType: "PAYMENT_RECORD",
        resourceId: id,
        oldValues: existingPayment,
        ipAddress: "127.0.0.1", // TODO: Get real IP
        userAgent: request.headers.get("user-agent") || "Unknown",
      },
    });

    return NextResponse.json({
      success: true,
      message: "Payment record deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting payment:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
