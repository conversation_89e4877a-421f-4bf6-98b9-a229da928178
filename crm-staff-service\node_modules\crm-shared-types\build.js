const fs = require('fs');
const path = require('path');

// Create dist directory
if (!fs.existsSync('dist')) {
  fs.mkdirSync('dist', { recursive: true });
}

// Copy source files to dist
function copyDir(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }
  
  const files = fs.readdirSync(src);
  
  for (const file of files) {
    const srcPath = path.join(src, file);
    const destPath = path.join(dest, file);
    
    if (fs.statSync(srcPath).isDirectory()) {
      copyDir(srcPath, destPath);
    } else if (file.endsWith('.ts')) {
      // Convert .ts to .js and copy
      const content = fs.readFileSync(srcPath, 'utf8');
      const jsContent = content
        .replace(/export \* from ['"](.+)['"];/g, 'module.exports = { ...module.exports, ...require("$1") };')
        .replace(/export \{([^}]+)\} from ['"](.+)['"];/g, 'const { $1 } = require("$2"); module.exports = { ...module.exports, $1 };')
        .replace(/export (interface|type|enum|const|function|class)/g, 'exports.$1 = $1; //')
        .replace(/import .+ from ['"](.+)['"];/g, 'const $1 = require("$1");');
      
      fs.writeFileSync(destPath.replace('.ts', '.js'), jsContent);
    }
  }
}

// Enhanced approach: create a comprehensive index.js that exports everything
const indexContent = `
// CRM Shared Types - Main Export
module.exports = {
  // Version information
  VERSION: '1.0.0',
  PACKAGE_NAME: 'crm-shared-types',
  
  // Service authentication utilities
  ServiceClient: class ServiceClient {
    constructor(baseUrl, serviceName, apiKey) {
      this.baseUrl = baseUrl.replace(/\\/$/, '');
      this.serviceName = serviceName;
      this.apiKey = apiKey;
    }
    
    getAuthHeaders() {
      const timestamp = Date.now();
      const payload = \`\${this.serviceName}:\${timestamp}\`;
      const signature = Buffer.from(\`\${payload}:\${this.apiKey}\`).toString('base64');
      
      return {
        'X-Service-Name': this.serviceName,
        'X-Service-Timestamp': timestamp.toString(),
        'X-Service-Signature': signature,
        'Content-Type': 'application/json',
      };
    }
    
    async get(endpoint) {
      const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });
      
      if (!response.ok) {
        throw new Error(\`Service request failed: \${response.status} \${response.statusText}\`);
      }
      
      return response.json();
    }
    
    async post(endpoint, data) {
      const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(\`Service request failed: \${response.status} \${response.statusText}\`);
      }
      
      return response.json();
    }
    
    async put(endpoint, data) {
      const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(\`Service request failed: \${response.status} \${response.statusText}\`);
      }
      
      return response.json();
    }
    
    async delete(endpoint) {
      const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });
      
      if (!response.ok) {
        throw new Error(\`Service request failed: \${response.status} \${response.statusText}\`);
      }
      
      return response.json();
    }
  },
  
  // Service client factory
  ServiceClientFactory: class ServiceClientFactory {
    constructor(config) {
      this.config = config;
    }
    
    getAdminClient() {
      if (!this.adminClient) {
        this.adminClient = new module.exports.AdminServiceClient(
          this.config.adminServiceUrl,
          this.config.serviceName,
          this.config.apiKey
        );
      }
      return this.adminClient;
    }
    
    getStaffClient() {
      if (!this.staffClient) {
        this.staffClient = new module.exports.StaffServiceClient(
          this.config.staffServiceUrl,
          this.config.serviceName,
          this.config.apiKey
        );
      }
      return this.staffClient;
    }
    
    getStudentClient() {
      if (!this.studentClient) {
        this.studentClient = new module.exports.StudentServiceClient(
          this.config.studentServiceUrl,
          this.config.serviceName,
          this.config.apiKey
        );
      }
      return this.studentClient;
    }
  },
  
  // Service authentication middleware
  createServiceAuthMiddleware: function(config) {
    return function serviceAuthMiddleware(request) {
      const serviceName = request.headers.get('X-Service-Name');
      const timestamp = request.headers.get('X-Service-Timestamp');
      const signature = request.headers.get('X-Service-Signature');
      
      const authenticatedRequest = request;
      
      if (!serviceName || !timestamp || !signature) {
        authenticatedRequest.serviceAuth = {
          serviceName: 'unknown',
          isAuthenticated: false,
        };
        return authenticatedRequest;
      }
      
      if (!config.allowedServices.includes(serviceName)) {
        authenticatedRequest.serviceAuth = {
          serviceName,
          isAuthenticated: false,
        };
        return authenticatedRequest;
      }
      
      // Simple signature verification
      const expectedPayload = \`\${serviceName}:\${timestamp}\`;
      const expectedSignature = Buffer.from(\`\${expectedPayload}:\${config.apiKey}\`).toString('base64');
      
      const now = Date.now();
      const timestampAge = now - parseInt(timestamp);
      const maxAge = 5 * 60 * 1000; // 5 minutes
      
      const isValid = signature === expectedSignature && timestampAge <= maxAge;
      
      authenticatedRequest.serviceAuth = {
        serviceName,
        isAuthenticated: isValid,
      };
      
      return authenticatedRequest;
    };
  }
};

// Extend ServiceClient for specific services
class AdminServiceClient extends module.exports.ServiceClient {
  async getPayments(page = 1, limit = 20) {
    return this.get(\`/api/payments?page=\${page}&limit=\${limit}\`);
  }
  
  async createPayment(data) {
    return this.post('/api/payments', data);
  }
  
  async createUser(userData) {
    return this.post('/api/users', userData);
  }
}

class StaffServiceClient extends module.exports.ServiceClient {
  async getLeads(status, page = 1, limit = 20) {
    const query = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
    if (status) query.append('status', status);
    return this.get(\`/api/leads?\${query}\`);
  }
  
  async getCourses(page = 1, limit = 20) {
    return this.get(\`/api/courses?page=\${page}&limit=\${limit}\`);
  }
  
  async getCourse(courseId) {
    return this.get(\`/api/courses/\${courseId}\`);
  }
  
  async getGroups(courseId) {
    const query = courseId ? \`?courseId=\${courseId}\` : '';
    return this.get(\`/api/groups\${query}\`);
  }
  
  async getAssignments(studentId) {
    const query = studentId ? \`?studentId=\${studentId}\` : '';
    return this.get(\`/api/assignments\${query}\`);
  }
  
  async getResources(courseId, groupId) {
    const query = new URLSearchParams();
    if (courseId) query.append('courseId', courseId);
    if (groupId) query.append('groupId', groupId);
    return this.get(\`/api/resources?\${query}\`);
  }
}

class StudentServiceClient extends module.exports.ServiceClient {
  async getStudent(studentId) {
    return this.get(\`/api/students/\${studentId}\`);
  }
  
  async createStudent(studentData) {
    return this.post('/api/students', studentData);
  }
  
  async getProgress(studentId) {
    return this.get(\`/api/progress?studentId=\${studentId}\`);
  }
  
  async updateProgress(progressData) {
    return this.post('/api/progress', progressData);
  }
  
  async syncSchedule(studentId) {
    return this.post('/api/schedule/sync', { studentId });
  }
}

module.exports.AdminServiceClient = AdminServiceClient;
module.exports.StaffServiceClient = StaffServiceClient;
module.exports.StudentServiceClient = StudentServiceClient;
`;

fs.writeFileSync('dist/index.js', indexContent);

// Create a simple index.d.ts
const dtsContent = `
export declare const VERSION: string;
export declare const PACKAGE_NAME: string;

export interface ServiceAuthConfig {
  serviceName: string;
  apiKey: string;
  allowedServices: string[];
}

export declare class ServiceClient {
  constructor(baseUrl: string, serviceName: string, apiKey: string);
  get<T>(endpoint: string): Promise<T>;
  post<T>(endpoint: string, data: any): Promise<T>;
  put<T>(endpoint: string, data: any): Promise<T>;
  delete<T>(endpoint: string): Promise<T>;
}

export declare class AdminServiceClient extends ServiceClient {
  getPayments(page?: number, limit?: number): Promise<any>;
  createPayment(data: any): Promise<any>;
  createUser(userData: any): Promise<any>;
}

export declare class StaffServiceClient extends ServiceClient {
  getLeads(status?: string, page?: number, limit?: number): Promise<any>;
  getCourses(page?: number, limit?: number): Promise<any>;
  getCourse(courseId: string): Promise<any>;
  getGroups(courseId?: string): Promise<any>;
  getAssignments(studentId?: string): Promise<any>;
  getResources(courseId?: string, groupId?: string): Promise<any>;
}

export declare class StudentServiceClient extends ServiceClient {
  getStudent(studentId: string): Promise<any>;
  createStudent(studentData: any): Promise<any>;
  getProgress(studentId: string): Promise<any>;
  updateProgress(progressData: any): Promise<any>;
  syncSchedule(studentId: string): Promise<any>;
}

export declare class ServiceClientFactory {
  constructor(config: {
    adminServiceUrl: string;
    staffServiceUrl: string;
    studentServiceUrl: string;
    serviceName: string;
    apiKey: string;
  });
  getAdminClient(): AdminServiceClient;
  getStaffClient(): StaffServiceClient;
  getStudentClient(): StudentServiceClient;
}

export declare function createServiceAuthMiddleware(config: ServiceAuthConfig): (request: any) => any;
`;

fs.writeFileSync('dist/index.d.ts', dtsContent);

console.log('Build completed successfully!');
