import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

// Webhook receiver for incoming events from other services
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { event } = body;

    // Verify webhook signature if secret is provided
    const signature = request.headers.get('X-Webhook-Signature');
    if (process.env.WEBHOOK_SECRET && signature) {
      // In a real implementation, verify the signature
      console.log('Webhook signature received:', signature);
    }

    // Process the webhook event
    await processWebhookEvent(event);

    return NextResponse.json({
      success: true,
      message: 'Webhook processed successfully',
    });
  } catch (error) {
    console.error('Webhook processing failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Webhook processing failed',
    }, { status: 500 });
  }
}

async function processWebhookEvent(event: any) {
  const { type, data } = event;

  switch (type) {
    case 'assignment.created':
    case 'assignment.updated':
      await handleAssignmentEvent(data);
      break;
    case 'course.created':
    case 'course.updated':
      await handleCourseEvent(data);
      break;
    case 'group.created':
    case 'group.updated':
      await handleGroupEvent(data);
      break;
    default:
      console.log(`Unhandled webhook event type: ${type}`);
  }
}

async function handleAssignmentEvent(data: any) {
  try {
    // Create or update student progress records for new assignments
    if (data.studentIds && Array.isArray(data.studentIds)) {
      for (const studentId of data.studentIds) {
        await db.studentProgress.upsert({
          where: {
            studentId_assignmentId: {
              studentId,
              assignmentId: data.id,
            },
          },
          update: {
            updatedAt: new Date(),
          },
          create: {
            studentId,
            assignmentId: data.id,
            progressPercentage: 0,
            status: 'assigned',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });
      }
    }

    console.log('Assignment event processed:', data.id);
  } catch (error) {
    console.error('Failed to handle assignment event:', error);
    throw error;
  }
}

async function handleCourseEvent(data: any) {
  try {
    // Update local course cache or trigger notifications
    console.log('Course event processed:', data.id);
    
    // Example: Update student schedules if course details changed
    if (data.schedule) {
      await db.studentSchedule.updateMany({
        where: {
          courseTitle: data.title,
        },
        data: {
          schedule: data.schedule,
          lastUpdated: new Date(),
        },
      });
    }
  } catch (error) {
    console.error('Failed to handle course event:', error);
    throw error;
  }
}

async function handleGroupEvent(data: any) {
  try {
    // Update student schedules based on group changes
    if (data.students && Array.isArray(data.students)) {
      for (const student of data.students) {
        await db.studentSchedule.upsert({
          where: {
            studentId_groupId: {
              studentId: student.id,
              groupId: data.id,
            },
          },
          update: {
            courseTitle: data.courseTitle || 'Unknown Course',
            teacherName: data.teacherName || 'Unknown Teacher',
            cabinetNumber: data.cabinetNumber || 'TBD',
            schedule: data.schedule || {},
            startDate: data.startDate ? new Date(data.startDate) : new Date(),
            endDate: data.endDate ? new Date(data.endDate) : null,
            lastUpdated: new Date(),
          },
          create: {
            studentId: student.id,
            groupId: data.id,
            courseTitle: data.courseTitle || 'Unknown Course',
            teacherName: data.teacherName || 'Unknown Teacher',
            cabinetNumber: data.cabinetNumber || 'TBD',
            schedule: data.schedule || {},
            startDate: data.startDate ? new Date(data.startDate) : new Date(),
            endDate: data.endDate ? new Date(data.endDate) : null,
            isActive: true,
            lastUpdated: new Date(),
          },
        });
      }
    }

    console.log('Group event processed:', data.id);
  } catch (error) {
    console.error('Failed to handle group event:', error);
    throw error;
  }
}
