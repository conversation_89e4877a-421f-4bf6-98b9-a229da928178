[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\index.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\auth.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\service-clients.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\assignments.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\courses.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\index.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\kpis.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\leads.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\payments.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\system.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\users.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\data-sync.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\webhooks.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\messaging.ts": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\notifications.ts": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\performance.ts": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\__tests__\\data-sync.test.ts": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\__tests__\\notifications.test.ts": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\__tests__\\service-clients.test.ts": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\__tests__\\setup.ts": "22"}, {"size": 393, "mtime": 1750448012916, "results": "23", "hashOfConfig": "24"}, {"size": 776, "mtime": 1750448088581, "results": "25", "hashOfConfig": "24"}, {"size": 1655, "mtime": 1750491477183, "results": "26", "hashOfConfig": "24"}, {"size": 4879, "mtime": 1750479424984, "results": "27", "hashOfConfig": "24"}, {"size": 9783, "mtime": 1750490776778, "results": "28", "hashOfConfig": "24"}, {"size": 3179, "mtime": 1750448143049, "results": "29", "hashOfConfig": "24"}, {"size": 2933, "mtime": 1750448126681, "results": "30", "hashOfConfig": "24"}, {"size": 2377, "mtime": 1750448214501, "results": "31", "hashOfConfig": "24"}, {"size": 3025, "mtime": 1750448178915, "results": "32", "hashOfConfig": "24"}, {"size": 1877, "mtime": 1750448112529, "results": "33", "hashOfConfig": "24"}, {"size": 3402, "mtime": 1750448160746, "results": "34", "hashOfConfig": "24"}, {"size": 3685, "mtime": 1750448199461, "results": "35", "hashOfConfig": "24"}, {"size": 2157, "mtime": 1750448101387, "results": "36", "hashOfConfig": "24"}, {"size": 9191, "mtime": 1750490922894, "results": "37", "hashOfConfig": "24"}, {"size": 8783, "mtime": 1750490944697, "results": "38", "hashOfConfig": "24"}, {"size": 11987, "mtime": 1750491347959, "results": "39", "hashOfConfig": "24"}, {"size": 9581, "mtime": 1750491131545, "results": "40", "hashOfConfig": "24"}, {"size": 9556, "mtime": 1750492456668, "results": "41", "hashOfConfig": "24"}, {"size": 7665, "mtime": 1750491671100, "results": "42", "hashOfConfig": "24"}, {"size": 10269, "mtime": 1750491707982, "results": "43", "hashOfConfig": "24"}, {"size": 8618, "mtime": 1750491640237, "results": "44", "hashOfConfig": "24"}, {"size": 860, "mtime": 1750491726351, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ba3sxi", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\service-clients.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\assignments.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\courses.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\kpis.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\leads.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\payments.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\system.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\users.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\data-sync.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\webhooks.ts", [], ["112"], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\messaging.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\notifications.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\performance.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\__tests__\\data-sync.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\__tests__\\notifications.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\__tests__\\service-clients.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\__tests__\\setup.ts", [], [], {"ruleId": "113", "severity": 2, "message": "114", "line": 244, "column": 20, "nodeType": "115", "messageId": "116", "endLine": 244, "endColumn": 37, "suppressions": "117"}, "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["118"], {"kind": "119", "justification": "120"}, "directive", ""]