// Webhook System for Real-time Inter-Service Communication

import { ServiceClientFactory } from './service-clients';
import { DataSyncManager, SyncEvent } from './data-sync';

export interface WebhookEvent {
  id: string;
  type: string;
  source: string;
  data: any;
  timestamp: Date;
  signature?: string;
}

export interface WebhookSubscription {
  id: string;
  url: string;
  events: string[];
  service: string;
  isActive: boolean;
  secret?: string;
}

export interface WebhookDelivery {
  id: string;
  subscriptionId: string;
  event: WebhookEvent;
  status: 'pending' | 'delivered' | 'failed' | 'retrying';
  attempts: number;
  lastAttempt?: Date;
  nextRetry?: Date;
  response?: {
    status: number;
    body: string;
  };
}

// Webhook manager for handling real-time notifications
export class WebhookManager {
  private subscriptions: Map<string, WebhookSubscription> = new Map();
  private deliveries: Map<string, WebhookDelivery> = new Map();
  private syncManager: DataSyncManager;

  constructor(
    private serviceFactory: ServiceClientFactory,
    syncManager?: DataSyncManager
  ) {
    this.syncManager = syncManager || new DataSyncManager(serviceFactory);
  }

  // Register a webhook subscription
  registerWebhook(subscription: Omit<WebhookSubscription, 'id'>): string {
    const id = this.generateWebhookId();
    const webhookSubscription: WebhookSubscription = {
      ...subscription,
      id,
    };
    
    this.subscriptions.set(id, webhookSubscription);
    return id;
  }

  // Unregister a webhook subscription
  unregisterWebhook(subscriptionId: string): boolean {
    return this.subscriptions.delete(subscriptionId);
  }

  // Emit a webhook event
  async emitEvent(event: Omit<WebhookEvent, 'id' | 'timestamp'>): Promise<void> {
    const webhookEvent: WebhookEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: new Date(),
    };

    // Find matching subscriptions
    const matchingSubscriptions = Array.from(this.subscriptions.values())
      .filter(sub => 
        sub.isActive && 
        sub.events.includes(event.type) &&
        sub.service !== event.source // Don't send to source service
      );

    // Create deliveries for each matching subscription
    const deliveryPromises = matchingSubscriptions.map(subscription =>
      this.createDelivery(subscription, webhookEvent)
    );

    await Promise.allSettled(deliveryPromises);

    // Also trigger data sync if applicable
    if (this.isSyncableEvent(event.type)) {
      await this.triggerDataSync(webhookEvent);
    }
  }

  // Create and execute webhook delivery
  private async createDelivery(
    subscription: WebhookSubscription,
    event: WebhookEvent
  ): Promise<void> {
    const deliveryId = this.generateDeliveryId();
    const delivery: WebhookDelivery = {
      id: deliveryId,
      subscriptionId: subscription.id,
      event,
      status: 'pending',
      attempts: 0,
    };

    this.deliveries.set(deliveryId, delivery);

    try {
      await this.executeDelivery(delivery, subscription);
    } catch (error) {
      console.error(`Webhook delivery failed: ${error}`);
      delivery.status = 'failed';
      this.scheduleRetry(delivery, subscription);
    }
  }

  // Execute webhook delivery
  private async executeDelivery(
    delivery: WebhookDelivery,
    subscription: WebhookSubscription
  ): Promise<void> {
    delivery.attempts++;
    delivery.lastAttempt = new Date();
    delivery.status = 'pending';

    const payload = {
      event: delivery.event,
      subscription: {
        id: subscription.id,
        events: subscription.events,
      },
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'CRM-Webhook/1.0',
      'X-Webhook-Event': delivery.event.type,
      'X-Webhook-Delivery': delivery.id,
    };

    // Add signature if secret is provided
    if (subscription.secret) {
      headers['X-Webhook-Signature'] = this.generateSignature(
        JSON.stringify(payload),
        subscription.secret
      );
    }

    const response = await fetch(subscription.url, {
      method: 'POST',
      headers,
      body: JSON.stringify(payload),
    });

    delivery.response = {
      status: response.status,
      body: await response.text(),
    };

    if (response.ok) {
      delivery.status = 'delivered';
    } else {
      delivery.status = 'failed';
      throw new Error(`HTTP ${response.status}: ${delivery.response.body}`);
    }
  }

  // Schedule retry for failed delivery
  private scheduleRetry(
    delivery: WebhookDelivery,
    subscription: WebhookSubscription
  ): void {
    const maxRetries = 5;
    const baseDelay = 1000; // 1 second

    if (delivery.attempts >= maxRetries) {
      delivery.status = 'failed';
      return;
    }

    delivery.status = 'retrying';
    const delay = baseDelay * Math.pow(2, delivery.attempts - 1); // Exponential backoff
    delivery.nextRetry = new Date(Date.now() + delay);

    setTimeout(async () => {
      try {
        await this.executeDelivery(delivery, subscription);
      } catch {
        this.scheduleRetry(delivery, subscription);
      }
    }, delay);
  }

  // Check if event should trigger data sync
  private isSyncableEvent(eventType: string): boolean {
    const syncableEvents = [
      'lead.created',
      'lead.updated',
      'student.created',
      'student.updated',
      'course.created',
      'course.updated',
      'group.created',
      'group.updated',
      'payment.created',
      'payment.updated',
      'assignment.created',
      'assignment.updated',
    ];

    return syncableEvents.includes(eventType);
  }

  // Trigger data synchronization
  private async triggerDataSync(event: WebhookEvent): Promise<void> {
    const [entity, action] = event.type.split('.');
    
    if (!entity || !action) {
      return;
    }

    const syncEvent: Omit<SyncEvent, 'id' | 'timestamp'> = {
      type: action as 'create' | 'update' | 'delete',
      entity: entity as any,
      entityId: event.data.id || event.data.entityId,
      data: event.data,
      sourceService: event.source,
      version: event.data.version || 1,
    };

    await this.syncManager.queueSync(syncEvent);
  }

  // Generate webhook signature
  private generateSignature(payload: string, secret: string): string {
    // In a real implementation, use HMAC-SHA256
    // For now, using a simple hash
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const crypto = require('crypto');
    return crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
  }

  // Generate unique IDs
  private generateWebhookId(): string {
    return `webhook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateDeliveryId(): string {
    return `delivery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get webhook statistics
  getWebhookStats(): {
    totalSubscriptions: number;
    activeSubscriptions: number;
    totalDeliveries: number;
    successfulDeliveries: number;
    failedDeliveries: number;
  } {
    const totalSubscriptions = this.subscriptions.size;
    const activeSubscriptions = Array.from(this.subscriptions.values())
      .filter(sub => sub.isActive).length;
    
    const deliveries = Array.from(this.deliveries.values());
    const totalDeliveries = deliveries.length;
    const successfulDeliveries = deliveries.filter(d => d.status === 'delivered').length;
    const failedDeliveries = deliveries.filter(d => d.status === 'failed').length;

    return {
      totalSubscriptions,
      activeSubscriptions,
      totalDeliveries,
      successfulDeliveries,
      failedDeliveries,
    };
  }

  // Get subscription details
  getSubscription(subscriptionId: string): WebhookSubscription | undefined {
    return this.subscriptions.get(subscriptionId);
  }

  // Get delivery details
  getDelivery(deliveryId: string): WebhookDelivery | undefined {
    return this.deliveries.get(deliveryId);
  }

  // List all subscriptions
  listSubscriptions(): WebhookSubscription[] {
    return Array.from(this.subscriptions.values());
  }

  // List recent deliveries
  listRecentDeliveries(limit: number = 50): WebhookDelivery[] {
    return Array.from(this.deliveries.values())
      .sort((a, b) => (b.lastAttempt?.getTime() || 0) - (a.lastAttempt?.getTime() || 0))
      .slice(0, limit);
  }
}

// Export factory function
export function createWebhookManager(
  serviceFactory: ServiceClientFactory,
  syncManager?: DataSyncManager
): WebhookManager {
  return new WebhookManager(serviceFactory, syncManager);
}
