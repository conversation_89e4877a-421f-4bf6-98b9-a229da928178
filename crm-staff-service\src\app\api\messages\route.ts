import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { MessagingManager, createMessagingManager } from 'crm-shared-types';

// Initialize messaging manager
const messagingManager = createMessagingManager();

// GET - Get user's message threads
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      }, { status: 401 });
    }

    const url = new URL(request.url);
    const unreadOnly = url.searchParams.get('unreadOnly') === 'true';
    const archived = url.searchParams.get('archived') === 'true';
    const searchQuery = url.searchParams.get('search');

    const threads = messagingManager.getUserThreads(session.user.id, {
      unreadOnly,
      archived,
      searchQuery: searchQuery || undefined,
    });

    const stats = messagingManager.getMessagingStats(session.user.id);

    return NextResponse.json({
      success: true,
      data: {
        threads,
        stats,
      },
    });
  } catch (error) {
    console.error('Failed to get message threads:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'MESSAGES_FETCH_FAILED',
        message: 'Failed to fetch message threads',
      },
    }, { status: 500 });
  }
}

// POST - Create new message thread
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      }, { status: 401 });
    }

    const body = await request.json();
    const { subject, participants, message, priority, tags } = body;

    if (!subject || !participants || !Array.isArray(participants) || participants.length === 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Subject and participants are required',
        },
      }, { status: 400 });
    }

    // Add current user to participants if not already included
    const currentUser = {
      userId: session.user.id,
      name: session.user.name || session.user.email,
      role: session.user.role || 'staff',
      service: 'crm-staff-service',
    };

    const allParticipants = [
      currentUser,
      ...participants.filter((p: any) => p.userId !== session.user.id),
    ];

    // Create thread
    const threadId = messagingManager.createThread(
      subject,
      allParticipants,
      session.user.id,
      {
        priority: priority || 'normal',
        tags: tags || [],
      }
    );

    // Send initial message if provided
    if (message) {
      const messageId = messagingManager.sendMessage(
        threadId,
        session.user.id,
        session.user.name || session.user.email,
        session.user.role || 'staff',
        message
      );

      return NextResponse.json({
        success: true,
        data: {
          threadId,
          messageId,
          message: 'Thread created and message sent successfully',
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        threadId,
        message: 'Thread created successfully',
      },
    });
  } catch (error) {
    console.error('Failed to create message thread:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'THREAD_CREATE_FAILED',
        message: error instanceof Error ? error.message : 'Failed to create thread',
      },
    }, { status: 500 });
  }
}
