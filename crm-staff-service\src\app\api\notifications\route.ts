import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NotificationManager, createNotificationManager, SimpleEmailProvider } from 'crm-shared-types';

// Initialize notification manager
const emailProvider = new SimpleEmailProvider({
  smtpHost: process.env.SMTP_HOST || 'localhost',
  smtpPort: parseInt(process.env.SMTP_PORT || '587'),
  username: process.env.SMTP_USERNAME || '',
  password: process.env.SMTP_PASSWORD || '',
  fromEmail: process.env.FROM_EMAIL || '<EMAIL>',
});

const notificationManager = createNotificationManager(emailProvider);

// Add default templates
notificationManager.addTemplate({
  id: 'lead_assigned',
  name: 'Lead Assigned',
  subject: 'New Lead Assigned: {{leadName}}',
  body: 'Hello {{staffName}},\n\nA new lead has been assigned to you:\n\nName: {{leadName}}\nEmail: {{leadEmail}}\nPhone: {{leadPhone}}\nSource: {{leadSource}}\n\nPlease follow up as soon as possible.\n\nBest regards,\nInnovative Centre Team',
  type: 'email',
  variables: ['staffName', 'leadName', 'leadEmail', 'leadPhone', 'leadSource'],
  isActive: true,
});

notificationManager.addTemplate({
  id: 'course_created',
  name: 'Course Created',
  subject: 'New Course Created: {{courseTitle}}',
  body: 'Hello {{staffName}},\n\nA new course has been created:\n\nTitle: {{courseTitle}}\nDescription: {{courseDescription}}\nDuration: {{courseDuration}} hours\nPrice: ${{coursePrice}}\n\nYou can now create groups for this course.\n\nBest regards,\nInnovative Centre Team',
  type: 'email',
  variables: ['staffName', 'courseTitle', 'courseDescription', 'courseDuration', 'coursePrice'],
  isActive: true,
});

notificationManager.addTemplate({
  id: 'assignment_reminder',
  name: 'Assignment Reminder',
  subject: 'Assignment Due Soon: {{assignmentTitle}}',
  body: 'Hello {{teacherName}},\n\nThis is a reminder that the following assignment is due soon:\n\nTitle: {{assignmentTitle}}\nDue Date: {{dueDate}}\nGroup: {{groupName}}\n\nPlease ensure all students have submitted their work.\n\nBest regards,\nInnovative Centre Team',
  type: 'email',
  variables: ['teacherName', 'assignmentTitle', 'dueDate', 'groupName'],
  isActive: true,
});

// GET - Get notifications for current user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      }, { status: 401 });
    }

    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    const status = url.searchParams.get('status');

    // Get user's notifications (in a real implementation, this would be from database)
    const stats = notificationManager.getNotificationStats();

    return NextResponse.json({
      success: true,
      data: {
        stats,
        notifications: [], // Would be populated from database
      },
    });
  } catch (error) {
    console.error('Failed to get notifications:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'NOTIFICATION_FETCH_FAILED',
        message: 'Failed to fetch notifications',
      },
    }, { status: 500 });
  }
}

// POST - Send notification
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      }, { status: 401 });
    }

    const body = await request.json();
    const { templateId, recipient, variables, type, subject, message, priority, scheduledAt } = body;

    let notificationId: string;

    if (templateId) {
      // Send using template
      notificationId = await notificationManager.sendNotification(
        templateId,
        recipient,
        variables || {},
        {
          priority: priority || 'normal',
          scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined,
        }
      );
    } else {
      // Send direct notification
      if (!type || !subject || !message) {
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_REQUEST',
            message: 'Type, subject, and message are required for direct notifications',
          },
        }, { status: 400 });
      }

      notificationId = await notificationManager.sendDirectNotification(
        type,
        recipient,
        subject,
        message,
        {
          priority: priority || 'normal',
          scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined,
        }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        notificationId,
        message: 'Notification sent successfully',
      },
    });
  } catch (error) {
    console.error('Failed to send notification:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'NOTIFICATION_SEND_FAILED',
        message: error instanceof Error ? error.message : 'Failed to send notification',
      },
    }, { status: 500 });
  }
}
