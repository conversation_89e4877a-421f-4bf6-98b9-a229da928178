(()=>{var e={};e.id=680,e.ids=[680],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},928:(e,t,r)=>{Promise.resolve().then(r.bind(r,6735))},1398:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=r(5239),n=r(8088),a=r(8170),i=r.n(a),o=r(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7578)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\auth\\signin\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\auth\\signin\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},1777:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3535:(e,t,r)=>{Promise.resolve().then(r.bind(r,2175))},3783:(e,t,r)=>{Promise.resolve().then(r.bind(r,9208))},3861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},4080:(e,t,r)=>{Promise.resolve().then(r.bind(r,7578))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>a});var s=r(7413),n=r(2175);r(5692);let a={title:"Admin Portal - Innovative Centre CRM",description:"Enhanced security admin portal for financial data management"};function i({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:"min-h-screen bg-gray-50",children:(0,s.jsx)(n.SessionProvider,{children:(0,s.jsx)("div",{className:"flex min-h-screen",children:(0,s.jsx)("main",{className:"flex-1",children:e})})})})})}},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(9384),n=r(2348);function a(...e){return(0,n.QP)((0,s.$)(e))}},5692:()=>{},6189:(e,t,r)=>{"use strict";var s=r(5773);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},6735:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var s=r(687),n=r(3210),a=r(9208),i=r(6189),o=r(9891),d=r(2688);let l=(0,d.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),c=(0,d.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var u=r(3861),m=r(9523),p=r(9667);r(1215);var v=r(1391),x=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,v.TL)(`Primitive.${t}`),a=n.forwardRef((e,n)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a?r:t,{...i,ref:n})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),h=n.forwardRef((e,t)=>(0,s.jsx)(x.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));h.displayName="Label";var g=r(4224),f=r(4780);let b=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),y=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(h,{ref:r,className:(0,f.cn)(b(),e),...t}));y.displayName=h.displayName;let j=(0,g.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function w({className:e,variant:t,...r}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,f.cn)(j({variant:t}),e),...r})}function P({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,f.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}function N(){let[e,t]=(0,n.useState)(""),[r,d]=(0,n.useState)(""),[v,x]=(0,n.useState)(""),[h,g]=(0,n.useState)(!1),[f,b]=(0,n.useState)(!1),[j,N]=(0,n.useState)(""),k=(0,i.useRouter)(),C=async t=>{t.preventDefault(),b(!0),N("");try{let t=await (0,a.Jv)("credentials",{email:e,password:r,mfaCode:v,redirect:!1});t?.error?N("Invalid credentials. Please try again."):await (0,a.Ht)()?k.push("/dashboard"):N("Authentication failed. Please try again.")}catch(e){N("An error occurred during sign in. Please try again.")}finally{b(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100",children:(0,s.jsx)(o.A,{className:"h-6 w-6 text-blue-600"})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Admin Portal Sign In"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Enhanced security portal for financial data management"})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,s.jsx)("span",{className:"text-blue-800 font-medium",children:"Enhanced Security"})]}),(0,s.jsx)("p",{className:"text-blue-700 text-sm mt-1",children:"All login attempts are monitored and logged for security purposes."})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:C,children:[j&&(0,s.jsxs)(w,{variant:"destructive",children:[(0,s.jsx)(l,{className:"h-4 w-4"}),(0,s.jsx)(P,{children:j})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(y,{htmlFor:"email",children:"Email Address"}),(0,s.jsx)(p.p,{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"mt-1",placeholder:"<EMAIL>"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(y,{htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:"relative mt-1",children:[(0,s.jsx)(p.p,{id:"password",name:"password",type:h?"text":"password",autoComplete:"current-password",required:!0,value:r,onChange:e=>d(e.target.value),placeholder:"Enter your password"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>g(!h),children:h?(0,s.jsx)(c,{className:"h-4 w-4 text-gray-400"}):(0,s.jsx)(u.A,{className:"h-4 w-4 text-gray-400"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(y,{htmlFor:"mfaCode",children:"MFA Code (Optional)"}),(0,s.jsx)(p.p,{id:"mfaCode",name:"mfaCode",type:"text",value:v,onChange:e=>x(e.target.value),className:"mt-1",placeholder:"Enter 6-digit MFA code",maxLength:6}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Multi-factor authentication will be required in production"})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)(m.$,{type:"submit",className:"w-full",disabled:f,children:f?"Signing in...":"Sign In"})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"This is a secure admin portal. Unauthorized access is prohibited."})})]})})}},7578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\auth\\signin\\page.tsx","default")},8225:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(687);r(3210);var n=r(1391),a=r(4224),i=r(4780);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:a=!1,...d}){let l=a?n.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...d})}},9667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(687),n=r(3210),a=r(4780);let i=n.forwardRef(({className:e,type:t,...r},n)=>(0,s.jsx)("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...r}));i.displayName="Input"},9891:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,542,108],()=>r(1398));module.exports=s})();